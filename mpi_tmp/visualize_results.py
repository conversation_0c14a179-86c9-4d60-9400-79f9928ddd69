#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
可视化MPI NTT并行性能分析结果
"""

import matplotlib.pyplot as plt
import numpy as np
import re
from pathlib import Path

def parse_log_file(filename):
    """解析单个log文件，提取测试数据"""
    data = []
    with open(filename, 'r', encoding='utf-8') as f:
        content = f.read()
        
    # 提取延迟数据的正则表达式
    latency_pattern = r'average latency for n=(\d+) p=(\d+) : ([\d.]+) us'
    matches = re.findall(latency_pattern, content)
    
    for match in matches:
        n, p, latency = int(match[0]), int(match[1]), float(match[2])
        data.append({'n': n, 'p': p, 'latency_us': latency})
    
    return data

def load_all_data():
    """加载所有log文件的数据"""
    log_files = {
        1: '1.log',
        2: '2.log', 
        4: '4.log',
        8: '8.log'
    }
    
    all_data = {}
    for processes, filename in log_files.items():
        try:
            data = parse_log_file(filename)
            all_data[processes] = data
            print(f"已加载 {filename}: {len(data)} 条记录")
        except FileNotFoundError:
            print(f"警告: 文件 {filename} 未找到")
    
    return all_data

def create_latency_comparison_chart(data):
    """创建延迟对比图表"""
    plt.figure(figsize=(14, 10))
    
    # 子图1: 小规模问题 (n=4)
    plt.subplot(2, 2, 1)
    processes = []
    latencies_small = []
    
    for proc_count in sorted(data.keys()):
        for record in data[proc_count]:
            if record['n'] == 4:
                processes.append(proc_count)
                latencies_small.append(record['latency_us'])
                break
    
    bars1 = plt.bar(range(len(processes)), latencies_small, 
                   color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
    plt.title('Small Scale Problem (n=4)', fontsize=12, fontweight='bold')
    plt.xlabel('Number of Processes')
    plt.ylabel('Latency (μs)')
    plt.xticks(range(len(processes)), [f'{p} proc' for p in processes])
    plt.grid(True, alpha=0.3)
    
    # 添加数值标签
    for i, v in enumerate(latencies_small):
        plt.text(i, v + max(latencies_small) * 0.01, f'{v:.1f}', 
                ha='center', va='bottom', fontsize=9)
    
    # 子图2: 大规模问题 (n=131072) - 按不同模数分组
    plt.subplot(2, 2, 2)
    
    # 组织大规模数据
    p_values = [7340033, 104857601, 469762049]
    p_labels = ['p=7.34M', 'p=104.9M', 'p=469.8M']
    
    process_counts = sorted(data.keys())
    x = np.arange(len(process_counts))
    width = 0.25
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c']
    
    for i, p_val in enumerate(p_values):
        latencies = []
        for proc_count in process_counts:
            found = False
            for record in data[proc_count]:
                if record['n'] == 131072 and record['p'] == p_val:
                    latencies.append(record['latency_us'])
                    found = True
                    break
            if not found:
                latencies.append(0)
        
        plt.bar(x + i * width, latencies, width, 
               label=p_labels[i], color=colors[i], alpha=0.8)
    
    plt.title('Large Scale Problem (n=131072)', fontsize=12, fontweight='bold')
    plt.xlabel('Number of Processes')
    plt.ylabel('Latency (μs)')
    plt.xticks(x + width, [f'{p}' for p in process_counts])
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 子图3: 加速比分析
    plt.subplot(2, 2, 3)
    
    for i, p_val in enumerate(p_values):
        speedups = []
        baseline_latency = None
        
        for proc_count in process_counts:
            for record in data[proc_count]:
                if record['n'] == 131072 and record['p'] == p_val:
                    if baseline_latency is None:  # 使用单进程作为基准
                        baseline_latency = record['latency_us']
                        speedups.append(1.0)
                    else:
                        speedup = baseline_latency / record['latency_us']
                        speedups.append(speedup)
                    break
        
        plt.plot(process_counts, speedups, 'o-', 
                label=p_labels[i], color=colors[i], linewidth=2, markersize=6)
    
    # 理想加速比线
    ideal_speedup = [p for p in process_counts]
    plt.plot(process_counts, ideal_speedup, '--', 
             color='black', alpha=0.7, label='Ideal Speedup')
    
    plt.title('Speedup Analysis (n=131072)', fontsize=12, fontweight='bold')
    plt.xlabel('Number of Processes')
    plt.ylabel('Speedup')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(process_counts)
    
    # 子图4: 效率分析
    plt.subplot(2, 2, 4)
    
    for i, p_val in enumerate(p_values):
        efficiencies = []
        baseline_latency = None
        
        for proc_count in process_counts:
            for record in data[proc_count]:
                if record['n'] == 131072 and record['p'] == p_val:
                    if baseline_latency is None:
                        baseline_latency = record['latency_us']
                        efficiencies.append(1.0)
                    else:
                        speedup = baseline_latency / record['latency_us']
                        efficiency = speedup / proc_count
                        efficiencies.append(efficiency)
                    break
        
        plt.plot(process_counts, efficiencies, 's-', 
                label=p_labels[i], color=colors[i], linewidth=2, markersize=6)
    
    plt.title('Parallel Efficiency (n=131072)', fontsize=12, fontweight='bold')
    plt.xlabel('Number of Processes')
    plt.ylabel('Efficiency')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.xticks(process_counts)
    plt.ylim(0, 1.1)
    
    plt.tight_layout()
    plt.savefig('mpi_ntt_performance_analysis.png', dpi=300, bbox_inches='tight')
    plt.show()

def create_detailed_comparison_table(data):
    """创建详细的性能对比表"""
    print("\n" + "="*80)
    print("MPI NTT Performance Analysis Results")
    print("="*80)
    
    # 小规模问题分析
    print("\n1. Small Scale Problem (n=4, p=7340033):")
    print("-" * 50)
    print(f"{'Processes':<12} {'Latency(μs)':<15} {'vs 1-proc':<15}")
    print("-" * 50)
    
    baseline_small = None
    for proc_count in sorted(data.keys()):
        for record in data[proc_count]:
            if record['n'] == 4:
                latency = record['latency_us']
                if baseline_small is None:
                    baseline_small = latency
                    ratio_str = "baseline"
                else:
                    ratio = latency / baseline_small
                    ratio_str = f"{ratio:.2f}x"
                
                print(f"{proc_count:<12} {latency:<15.2f} {ratio_str:<15}")
                break
    
    # 大规模问题分析
    print("\n2. Large Scale Problem (n=131072):")
    print("-" * 80)
    
    p_values = [7340033, 104857601, 469762049]
    p_labels = ['p=7340033', 'p=104857601', 'p=469762049']
    
    for i, (p_val, p_label) in enumerate(zip(p_values, p_labels)):
        print(f"\n{p_label}:")
        print(f"{'Processes':<12} {'Latency(μs)':<15} {'Speedup':<12} {'Efficiency':<12}")
        print("-" * 60)
        
        baseline_large = None
        for proc_count in sorted(data.keys()):
            for record in data[proc_count]:
                if record['n'] == 131072 and record['p'] == p_val:
                    latency = record['latency_us']
                    
                    if baseline_large is None:
                        baseline_large = latency
                        speedup = 1.0
                        efficiency = 1.0
                    else:
                        speedup = baseline_large / latency
                        efficiency = speedup / proc_count
                    
                    print(f"{proc_count:<12} {latency:<15.1f} {speedup:<12.2f} {efficiency:<12.3f}")
                    break

def main():
    """主函数"""
    print("MPI NTT Performance Visualization")
    print("=" * 40)
    
    # 加载数据
    data = load_all_data()
    
    if not data:
        print("错误: 没有找到任何数据文件")
        return
    
    # 设置中文字体支持
    plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
    plt.rcParams['axes.unicode_minus'] = False
    
    # 创建可视化图表
    create_latency_comparison_chart(data)
    
    # 输出详细分析表格
    create_detailed_comparison_table(data)
    
    print(f"\n图表已保存为: mpi_ntt_performance_analysis.png")

if __name__ == "__main__":
    main() 