并行NTT性能分析报告
==================================================
生成时间: 2025-05-31 15:04:44

1. 测试概述
--------------------
测试进程数: [1, 2, 4, 8]
测试用例数: 4
数据规模: [4, 131072]

2. 性能分析
--------------------
大规模数据性能 (n >= 1000):
  最大加速比: 1.63x (进程数: 4, n=131072)
  最佳效率: 88.02% (进程数: 1, n=131072)

3. 详细结果
--------------------

测试用例 0 (n=4, p=7340033):
进程数      串行(us)       并行(us)       加速比      效率(%)   
--------------------------------------------------
1        3.65         38.86        0.09     9.39    
2        3.72         105.80       0.04     1.76    
4        3.83         94.45        0.04     1.01    
8        3.73         143.01       0.03     0.33    

测试用例 1 (n=131072, p=7340033):
进程数      串行(us)       并行(us)       加速比      效率(%)   
--------------------------------------------------
1        96040.40     109831.40    0.87     87.44   
2        95558.64     79026.80     1.21     60.46   
4        95422.93     60601.26     1.57     39.37   
8        95549.73     62060.24     1.54     19.25   

测试用例 2 (n=131072, p=104857601):
进程数      串行(us)       并行(us)       加速比      效率(%)   
--------------------------------------------------
1        96223.43     109375.24    0.88     87.98   
2        95630.85     78901.25     1.21     60.60   
4        95599.86     58561.67     1.63     40.81   
8        95302.98     58747.01     1.62     20.28   

测试用例 3 (n=131072, p=469762049):
进程数      串行(us)       并行(us)       加速比      效率(%)   
--------------------------------------------------
1        96355.82     109465.15    0.88     88.02   
2        95673.84     78272.81     1.22     61.12   
4        96178.53     60003.34     1.60     40.07   
8        95715.51     60343.63     1.59     19.83   
