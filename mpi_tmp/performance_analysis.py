#!/usr/bin/env python3
"""
并行NTT性能分析脚本
分析不同进程数下的性能表现，生成性能报告和可视化图表
"""

import subprocess
import re
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

def run_ntt_test(num_procs):
    """运行指定进程数的NTT测试"""
    cmd = f"mpirun -np {num_procs} ./ntt_parallel"
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, timeout=120)
        return result.stdout
    except subprocess.TimeoutExpired:
        return f"Timeout for {num_procs} processes"

def parse_results(output):
    """解析测试输出，提取性能数据"""
    results = []
    
    # 提取进程数
    rank_match = re.search(r'ranks = (\d+)', output)
    num_procs = int(rank_match.group(1)) if rank_match else 0
    
    # 提取每个测试用例的结果
    test_pattern = r'测试用例 (\d+) \(n=(\d+), p=(\d+)\):\s*串行NTT:\s*([\d.]+) us\s*并行NTT:\s*([\d.]+) us\s*加速比:\s*([\d.]+)x\s*效率:\s*([\d.]+)%'
    
    matches = re.findall(test_pattern, output)
    
    for match in matches:
        test_id, n, p, serial_time, parallel_time, speedup, efficiency = match
        results.append({
            'num_procs': num_procs,
            'test_id': int(test_id),
            'n': int(n),
            'p': int(p),
            'serial_time': float(serial_time),
            'parallel_time': float(parallel_time),
            'speedup': float(speedup),
            'efficiency': float(efficiency)
        })
    
    return results

def main():
    print("开始并行NTT性能分析...")
    print("=" * 60)
    
    # 测试不同的进程数
    process_counts = [1, 2, 4, 8]
    all_results = []
    
    for num_procs in process_counts:
        print(f"测试 {num_procs} 进程...")
        output = run_ntt_test(num_procs)
        results = parse_results(output)
        all_results.extend(results)
        print(f"完成 {num_procs} 进程测试")
    
    if not all_results:
        print("未能获取到有效的测试数据")
        return

    print("\n性能分析结果:")
    print("=" * 60)

    # 按测试用例分组分析
    test_ids = set(r['test_id'] for r in all_results)
    for test_id in sorted(test_ids):
        test_data = [r for r in all_results if r['test_id'] == test_id]
        if not test_data:
            continue

        n = test_data[0]['n']
        p = test_data[0]['p']

        print(f"\n测试用例 {test_id} (n={n}, p={p}):")
        print("-" * 40)
        print(f"{'进程数':<8} {'串行时间(us)':<12} {'并行时间(us)':<12} {'加速比':<8} {'效率(%)':<8}")
        print("-" * 40)

        for row in sorted(test_data, key=lambda x: x['num_procs']):
            print(f"{row['num_procs']:<8} {row['serial_time']:<12.2f} {row['parallel_time']:<12.2f} "
                  f"{row['speedup']:<8.2f} {row['efficiency']:<8.2f}")

    # 生成性能图表
    create_performance_plots(all_results)

    # 生成性能报告
    generate_performance_report(all_results)

def create_performance_plots(all_results):
    """生成性能分析图表"""
    # 只分析大规模数据（n >= 1000）
    large_data = [r for r in all_results if r['n'] >= 1000]

    if not large_data:
        print("没有大规模数据用于绘图")
        return
    
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 加速比 vs 进程数
    test_ids = set(r['test_id'] for r in large_data)
    for test_id in sorted(test_ids):
        test_data = [r for r in large_data if r['test_id'] == test_id]
        test_data = sorted(test_data, key=lambda x: x['num_procs'])
        n = test_data[0]['n']
        procs = [r['num_procs'] for r in test_data]
        speedups = [r['speedup'] for r in test_data]
        ax1.plot(procs, speedups, marker='o', label=f'Test {test_id} (n={n})')

    ax1.plot([1, 8], [1, 8], 'k--', alpha=0.5, label='理想加速比')
    ax1.set_xlabel('进程数')
    ax1.set_ylabel('加速比')
    ax1.set_title('加速比 vs 进程数')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 2. 效率 vs 进程数
    for test_id in sorted(test_ids):
        test_data = [r for r in large_data if r['test_id'] == test_id]
        test_data = sorted(test_data, key=lambda x: x['num_procs'])
        n = test_data[0]['n']
        procs = [r['num_procs'] for r in test_data]
        efficiencies = [r['efficiency'] for r in test_data]
        ax2.plot(procs, efficiencies, marker='s', label=f'Test {test_id} (n={n})')

    ax2.axhline(y=100, color='k', linestyle='--', alpha=0.5, label='理想效率')
    ax2.set_xlabel('进程数')
    ax2.set_ylabel('效率 (%)')
    ax2.set_title('并行效率 vs 进程数')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 执行时间对比
    proc_counts = sorted(set(r['num_procs'] for r in large_data))
    test_ids = sorted(set(r['test_id'] for r in large_data))

    x = np.arange(len(proc_counts))
    width = 0.35

    for i, test_id in enumerate(test_ids):
        test_data = [r for r in large_data if r['test_id'] == test_id]
        test_data = sorted(test_data, key=lambda x: x['num_procs'])

        if len(test_data) > 0:
            n = test_data[0]['n']
            parallel_times = [r['parallel_time'] for r in test_data]
            ax3.bar(x + i*width, parallel_times, width,
                   label=f'Test {test_id} (n={n})', alpha=0.8)

    ax3.set_xlabel('进程数')
    ax3.set_ylabel('并行执行时间 (us)')
    ax3.set_title('并行执行时间对比')
    ax3.set_xticks(x + width/2)
    ax3.set_xticklabels(proc_counts)
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 4. 强扩展性分析
    for test_id in sorted(test_ids):
        test_data = [r for r in large_data if r['test_id'] == test_id]
        test_data = sorted(test_data, key=lambda x: x['num_procs'])
        n = test_data[0]['n']
        baseline_time = next(r['parallel_time'] for r in test_data if r['num_procs'] == 1)
        procs = [r['num_procs'] for r in test_data]
        normalized_times = [r['parallel_time'] / baseline_time for r in test_data]
        ax4.plot(procs, normalized_times, marker='^', label=f'Test {test_id} (n={n})')

    ideal_x = np.array([1, 2, 4, 8])
    ideal_y = 1.0 / ideal_x
    ax4.plot(ideal_x, ideal_y, 'k--', alpha=0.5, label='理想强扩展性')
    ax4.set_xlabel('进程数')
    ax4.set_ylabel('归一化执行时间')
    ax4.set_title('强扩展性分析')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('parallel_ntt_performance.png', dpi=300, bbox_inches='tight')
    print(f"\n性能图表已保存为: parallel_ntt_performance.png")

def generate_performance_report(all_results):
    """生成详细的性能报告"""
    report_file = "parallel_ntt_report.txt"

    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("并行NTT性能分析报告\n")
        f.write("=" * 50 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        f.write("1. 测试概述\n")
        f.write("-" * 20 + "\n")
        proc_counts = sorted(set(r['num_procs'] for r in all_results))
        test_ids = sorted(set(r['test_id'] for r in all_results))
        data_sizes = sorted(set(r['n'] for r in all_results))
        f.write(f"测试进程数: {proc_counts}\n")
        f.write(f"测试用例数: {len(test_ids)}\n")
        f.write(f"数据规模: {data_sizes}\n\n")

        f.write("2. 性能分析\n")
        f.write("-" * 20 + "\n")

        # 大规模数据分析
        large_data = [r for r in all_results if r['n'] >= 1000]
        if large_data:
            f.write("大规模数据性能 (n >= 1000):\n")

            max_speedup = max(r['speedup'] for r in large_data)
            max_speedup_row = next(r for r in large_data if r['speedup'] == max_speedup)
            f.write(f"  最大加速比: {max_speedup:.2f}x (进程数: {max_speedup_row['num_procs']}, n={max_speedup_row['n']})\n")

            best_efficiency = max(r['efficiency'] for r in large_data)
            best_eff_row = next(r for r in large_data if r['efficiency'] == best_efficiency)
            f.write(f"  最佳效率: {best_efficiency:.2f}% (进程数: {best_eff_row['num_procs']}, n={best_eff_row['n']})\n")

            f.write("\n")

        f.write("3. 详细结果\n")
        f.write("-" * 20 + "\n")

        for test_id in sorted(test_ids):
            test_data = [r for r in all_results if r['test_id'] == test_id]
            test_data = sorted(test_data, key=lambda x: x['num_procs'])
            if not test_data:
                continue

            n = test_data[0]['n']
            p = test_data[0]['p']

            f.write(f"\n测试用例 {test_id} (n={n}, p={p}):\n")
            f.write(f"{'进程数':<8} {'串行(us)':<12} {'并行(us)':<12} {'加速比':<8} {'效率(%)':<8}\n")
            f.write("-" * 50 + "\n")

            for row in test_data:
                f.write(f"{row['num_procs']:<8} {row['serial_time']:<12.2f} {row['parallel_time']:<12.2f} "
                       f"{row['speedup']:<8.2f} {row['efficiency']:<8.2f}\n")

    print(f"详细报告已保存为: {report_file}")

if __name__ == "__main__":
    main()
