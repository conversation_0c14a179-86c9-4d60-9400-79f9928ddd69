#include <hip/hip_runtime.h>
#include <iostream>
#include <chrono>
#include <vector>
#include <cmath>
#include <random>

/**
 * @brief 性能测量工具类
 */
class GPUPerformanceBenchmark {
private:
    hipEvent_t start_event, stop_event;
    std::chrono::high_resolution_clock::time_point cpu_start;
    std::chrono::high_resolution_clock::time_point cpu_stop;
    
public:
    GPUPerformanceBenchmark() {
        hipEventCreate(&start_event);
        hipEventCreate(&stop_event);
    }
    
    ~GPUPerformanceBenchmark() {
        hipEventDestroy(start_event);
        hipEventDestroy(stop_event);
    }
    
    void start_gpu_timer() {
        hipEventRecord(start_event, 0);
    }
    
    float stop_gpu_timer() {
        hipEventRecord(stop_event, 0);
        hipEventSynchronize(stop_event);
        float ms = 0;
        hipEventElapsedTime(&ms, start_event, stop_event);
        return ms;
    }
    
    void start_cpu_timer() {
        cpu_start = std::chrono::high_resolution_clock::now();
    }
    
    double stop_cpu_timer() {
        cpu_stop = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(cpu_stop - cpu_start);
        return duration.count() / 1000.0;
    }
    
    double calculate_gflops(long long ops, double time_ms) {
        return (ops / 1e9) / (time_ms / 1000.0);
    }
    
    double calculate_bandwidth_gb_s(long long bytes, double time_ms) {
        return (bytes / 1e9) / (time_ms / 1000.0);
    }
};

// 优化参数定义
#define TILE_SIZE 16
#define WARP_SIZE 64

/**
 * @brief 原始简单矩阵乘法内核（基准版本）
 */
__global__ void naive_matmul(const double* A, const double* B, double* C, int M, int N, int K) {
    int row = blockIdx.y * blockDim.y + threadIdx.y;
    int col = blockIdx.x * blockDim.x + threadIdx.x;

    if (row < M && col < N) {
        double sum = 0.0;
        for (int i = 0; i < K; ++i) {
            sum += A[row * K + i] * B[i * N + col];
        }
        C[row * N + col] = sum;
    }
}

/**
 * @brief 优化的平铺矩阵乘法内核（目标版本）
 */
__global__ void optimized_matmul(const double* __restrict__ A, 
                                const double* __restrict__ B, 
                                double* __restrict__ C, 
                                int M, int N, int K) {
    __shared__ double As[TILE_SIZE][TILE_SIZE + 1];
    __shared__ double Bs[TILE_SIZE][TILE_SIZE + 1];
    
    const int tx = threadIdx.x;
    const int ty = threadIdx.y;
    const int bx = blockIdx.x;
    const int by = blockIdx.y;
    
    const int row = by * TILE_SIZE + ty;
    const int col = bx * TILE_SIZE + tx;
    
    double sum = 0.0;
    
    for (int tile_idx = 0; tile_idx < (K + TILE_SIZE - 1) / TILE_SIZE; ++tile_idx) {
        int k_start = tile_idx * TILE_SIZE;
        
        if (row < M && (k_start + tx) < K) {
            As[ty][tx] = A[row * K + k_start + tx];
        } else {
            As[ty][tx] = 0.0;
        }
        
        if ((k_start + ty) < K && col < N) {
            Bs[ty][tx] = B[(k_start + ty) * N + col];
        } else {
            Bs[ty][tx] = 0.0;
        }
        
        __syncthreads();
        
        for (int k = 0; k < TILE_SIZE; ++k) {
            sum += As[ty][k] * Bs[k][tx];
        }
        
        __syncthreads();
    }
    
    if (row < M && col < N) {
        C[row * N + col] = sum;
    }
}

/**
 * @brief 简单ReLU激活函数
 */
__global__ void simple_relu(double* x, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        x[idx] = fmax(0.0, x[idx]);
    }
}

/**
 * @brief 向量化ReLU激活函数
 */
__global__ void vectorized_relu(double* __restrict__ x, int size) {
    const int idx = blockIdx.x * blockDim.x + threadIdx.x;
    const int stride = gridDim.x * blockDim.x;
    
    for (int i = idx; i < size; i += stride) {
        x[i] = fmax(0.0, x[i]);
    }
}

/**
 * @brief 执行矩阵乘法性能测试
 */
void benchmark_matmul(int M, int N, int K, int num_iterations = 100) {
    std::cout << "\n=== 矩阵乘法性能测试 (" << M << "x" << K << " * " << K << "x" << N << ") ===" << std::endl;
    
    size_t size_A = M * K * sizeof(double);
    size_t size_B = K * N * sizeof(double);
    size_t size_C = M * N * sizeof(double);
    
    // 分配和初始化主机内存
    std::vector<double> h_A(M * K), h_B(K * N), h_C_naive(M * N), h_C_opt(M * N);
    
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<double> dis(-1.0, 1.0);
    
    for (int i = 0; i < M * K; ++i) h_A[i] = dis(gen);
    for (int i = 0; i < K * N; ++i) h_B[i] = dis(gen);
    
    // 分配GPU内存
    double *d_A, *d_B, *d_C_naive, *d_C_opt;
    hipMalloc(&d_A, size_A);
    hipMalloc(&d_B, size_B);
    hipMalloc(&d_C_naive, size_C);
    hipMalloc(&d_C_opt, size_C);
    
    // 复制数据到GPU
    hipMemcpy(d_A, h_A.data(), size_A, hipMemcpyHostToDevice);
    hipMemcpy(d_B, h_B.data(), size_B, hipMemcpyHostToDevice);
    
    GPUPerformanceBenchmark benchmark;
    
    // 测试简单版本
    dim3 block_naive(16, 16);
    dim3 grid_naive((N + 15) / 16, (M + 15) / 16);
    
    hipDeviceSynchronize();
    benchmark.start_gpu_timer();
    
    for (int i = 0; i < num_iterations; ++i) {
        naive_matmul<<<grid_naive, block_naive>>>(d_A, d_B, d_C_naive, M, N, K);
    }
    
    float naive_time = benchmark.stop_gpu_timer();
    
    // 测试优化版本
    dim3 block_opt(TILE_SIZE, TILE_SIZE);
    dim3 grid_opt((N + TILE_SIZE - 1) / TILE_SIZE, (M + TILE_SIZE - 1) / TILE_SIZE);
    
    hipDeviceSynchronize();
    benchmark.start_gpu_timer();
    
    for (int i = 0; i < num_iterations; ++i) {
        optimized_matmul<<<grid_opt, block_opt>>>(d_A, d_B, d_C_opt, M, N, K);
    }
    
    float opt_time = benchmark.stop_gpu_timer();
    
    // 验证结果正确性
    hipMemcpy(h_C_naive.data(), d_C_naive, size_C, hipMemcpyDeviceToHost);
    hipMemcpy(h_C_opt.data(), d_C_opt, size_C, hipMemcpyDeviceToHost);
    
    double max_error = 0.0;
    for (int i = 0; i < M * N; ++i) {
        double error = std::abs(h_C_naive[i] - h_C_opt[i]);
        max_error = std::max(max_error, error);
    }
    
    // 计算性能指标
    long long ops = 2LL * M * N * K * num_iterations;
    long long bytes = (size_A + size_B + size_C) * num_iterations;
    
    double naive_gflops = benchmark.calculate_gflops(ops, naive_time);
    double opt_gflops = benchmark.calculate_gflops(ops, opt_time);
    double naive_bandwidth = benchmark.calculate_bandwidth_gb_s(bytes, naive_time);
    double opt_bandwidth = benchmark.calculate_bandwidth_gb_s(bytes, opt_time);
    
    std::cout << "简单版本:" << std::endl;
    std::cout << "  时间: " << naive_time << " ms" << std::endl;
    std::cout << "  性能: " << naive_gflops << " GFLOPS" << std::endl;
    std::cout << "  带宽: " << naive_bandwidth << " GB/s" << std::endl;
    
    std::cout << "优化版本:" << std::endl;
    std::cout << "  时间: " << opt_time << " ms" << std::endl;
    std::cout << "  性能: " << opt_gflops << " GFLOPS" << std::endl;
    std::cout << "  带宽: " << opt_bandwidth << " GB/s" << std::endl;
    
    std::cout << "性能提升:" << std::endl;
    std::cout << "  加速比: " << naive_time / opt_time << "x" << std::endl;
    std::cout << "  GFLOPS提升: " << (opt_gflops / naive_gflops) << "x" << std::endl;
    std::cout << "  最大误差: " << max_error << std::endl;
    
    // 清理内存
    hipFree(d_A);
    hipFree(d_B);
    hipFree(d_C_naive);
    hipFree(d_C_opt);
}

/**
 * @brief 执行激活函数性能测试
 */
void benchmark_activation(int size, int num_iterations = 1000) {
    std::cout << "\n=== ReLU激活函数性能测试 (大小: " << size << ") ===" << std::endl;
    
    size_t bytes = size * sizeof(double);
    
    // 分配和初始化数据
    std::vector<double> h_data(size);
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_real_distribution<double> dis(-2.0, 2.0);
    
    for (int i = 0; i < size; ++i) {
        h_data[i] = dis(gen);
    }
    
    double *d_data_simple, *d_data_opt;
    hipMalloc(&d_data_simple, bytes);
    hipMalloc(&d_data_opt, bytes);
    
    GPUPerformanceBenchmark benchmark;
    
    // 测试简单版本
    hipMemcpy(d_data_simple, h_data.data(), bytes, hipMemcpyHostToDevice);
    
    int blocks_simple = (size + 255) / 256;
    
    hipDeviceSynchronize();
    benchmark.start_gpu_timer();
    
    for (int i = 0; i < num_iterations; ++i) {
        simple_relu<<<blocks_simple, 256>>>(d_data_simple, size);
    }
    
    float simple_time = benchmark.stop_gpu_timer();
    
    // 测试优化版本
    hipMemcpy(d_data_opt, h_data.data(), bytes, hipMemcpyHostToDevice);
    
    int blocks_opt = std::min((size + 255) / 256, 1024);
    
    hipDeviceSynchronize();
    benchmark.start_gpu_timer();
    
    for (int i = 0; i < num_iterations; ++i) {
        vectorized_relu<<<blocks_opt, 256>>>(d_data_opt, size);
    }
    
    float opt_time = benchmark.stop_gpu_timer();
    
    // 计算带宽利用率
    long long total_bytes = bytes * num_iterations * 2; // 读写各一次
    double simple_bandwidth = benchmark.calculate_bandwidth_gb_s(total_bytes, simple_time);
    double opt_bandwidth = benchmark.calculate_bandwidth_gb_s(total_bytes, opt_time);
    
    std::cout << "简单版本:" << std::endl;
    std::cout << "  时间: " << simple_time << " ms" << std::endl;
    std::cout << "  带宽: " << simple_bandwidth << " GB/s" << std::endl;
    
    std::cout << "优化版本:" << std::endl;
    std::cout << "  时间: " << opt_time << " ms" << std::endl;
    std::cout << "  带宽: " << opt_bandwidth << " GB/s" << std::endl;
    
    std::cout << "性能提升:" << std::endl;
    std::cout << "  加速比: " << simple_time / opt_time << "x" << std::endl;
    std::cout << "  带宽提升: " << (opt_bandwidth / simple_bandwidth) << "x" << std::endl;
    
    hipFree(d_data_simple);
    hipFree(d_data_opt);
}

/**
 * @brief 获取GPU设备信息
 */
void print_device_info() {
    int device_count;
    hipGetDeviceCount(&device_count);
    
    std::cout << "=== GPU设备信息 ===" << std::endl;
    std::cout << "检测到 " << device_count << " 个GPU设备" << std::endl;
    
    if (device_count > 0) {
        hipDeviceProp_t prop;
        hipGetDeviceProperties(&prop, 0);
        
        std::cout << "设备0: " << prop.name << std::endl;
        std::cout << "  计算能力: " << prop.major << "." << prop.minor << std::endl;
        std::cout << "  全局内存: " << (prop.totalGlobalMem / (1024 * 1024)) << " MB" << std::endl;
        std::cout << "  共享内存/块: " << (prop.sharedMemPerBlock / 1024) << " KB" << std::endl;
        std::cout << "  最大线程/块: " << prop.maxThreadsPerBlock << std::endl;
        std::cout << "  warp大小: " << prop.warpSize << std::endl;
        std::cout << "  多处理器数量: " << prop.multiProcessorCount << std::endl;
        
        float peak_bandwidth = 2.0 * prop.memoryClockRate * (prop.memoryBusWidth / 8) / 1.0e6;
        std::cout << "  理论峰值带宽: " << peak_bandwidth << " GB/s" << std::endl;
    }
}

int main() {
    std::cout << "HIP GPU 性能基准测试工具" << std::endl;
    std::cout << "===========================" << std::endl;
    
    print_device_info();
    
    // 针对您的MLP网络维度进行测试
    std::cout << "\n### MLP网络相关测试 ###" << std::endl;
    
    // 输入到隐藏层的矩阵乘法 (BATCH_SIZE=256, INPUT_DIM=10, HIDDEN_DIM=32)
    benchmark_matmul(256, 32, 10, 200);
    
    // 隐藏层到输出层的矩阵乘法 (BATCH_SIZE=256, HIDDEN_DIM=32, OUTPUT_DIM=1)
    benchmark_matmul(256, 1, 32, 200);
    
    // 隐藏层ReLU激活函数测试
    benchmark_activation(256 * 32, 500);
    
    // 额外的大规模测试
    std::cout << "\n### 大规模矩阵测试 ###" << std::endl;
    benchmark_matmul(1024, 1024, 1024, 10);
    benchmark_matmul(2048, 2048, 2048, 5);
    
    // 不同大小的激活函数测试
    std::cout << "\n### 不同规模激活函数测试 ###" << std::endl;
    benchmark_activation(1024 * 1024, 100);
    benchmark_activation(4 * 1024 * 1024, 50);
    
    std::cout << "\n性能测试完成!" << std::endl;
    
    return 0;
} 