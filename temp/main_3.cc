#include <hip/hip_runtime.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <cmath>
#include <chrono>
#include <string>
#include <sstream>
#include <algorithm>

// 编译文件
// hipcc sourcefile_mlp.cpp -o mlp_full_dcu
// 执行文件
// ./mlp_full_dcu 或者 hipprof ./mlp_full_dcu

// 预定义参数，可根据需求修改
#define INPUT_DIM 10
#define HIDDEN_DIM 32
#define OUTPUT_DIM 1
#define BATCH_SIZE 256
#define EPOCHS 200
#define LEARNING_RATE 1e-4


// 以下函数和main函数均不为固定形式，可自行按照需求修改

// HIP kernels函数形式，需要自行设计
__global__ void matmul(const double* A, const double* B, double* C, int M, int N, int K) {
    int row = blockIdx.y * blockDim.y + threadIdx.y;
    int col = blockIdx.x * blockDim.x + threadIdx.x;

    if (row < M && col < K) {
        double sum = 0.0;
        for (int i = 0; i < N; ++i) {
            sum += A[row * N + i] * B[i * K + col];
        }
        C[row * K + col] = sum;
    }
}

__global__ void compute_output_grad(const double* pred, const double* target, double* grad, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        grad[idx] = 2.0 * (pred[idx] - target[idx]); // Factor of 1/N can be handled in learning rate or on host
    }
}

__global__ void compute_relu_backward(double* delta, const double* activ, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        if (activ[idx] <= 0) {
            delta[idx] = 0;
        }
        // if activ[idx] > 0, delta[idx] remains unchanged
    }
}

__global__ void compute_mse_loss(const double* pred, const double* target, double* total_loss_device, int batch_size, int output_dim) {
    // This kernel computes the sum of squared errors for the batch.
    // The final division by batch_size (or batch_size * output_dim) should be done on the host.
    // It's assumed total_loss_device is initialized to 0.
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < batch_size * output_dim) {
        double diff = pred[idx] - target[idx];
        atomicAdd(total_loss_device, diff * diff);
    }
}

__global__ void sgd_update(double* weights, const double* grad, double lr, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        weights[idx] -= lr * grad[idx];
    }
}

// 加载带宽数据
std::vector<double> load_json_bandwidth(const std::string& filename) {
    std::vector<double> data;
    std::ifstream file(filename);
    std::string line;
    
    if (file.is_open()) {
        std::getline(file, line); // Read the entire line
        file.close();

        // Remove brackets and split by comma
        if (!line.empty() && line.front() == '[' && line.back() == ']') {
            line = line.substr(1, line.length() - 2); // Remove [ and ]
        } else {
            std::cerr << "[WARNING] JSON file " << filename << " does not start/end with brackets as expected. Attempting to parse anyway." << std::endl;
        }

        std::stringstream ss(line);
        std::string item;
        while (std::getline(ss, item, ',')) {
            try {
                // Trim whitespace that might be around the number
                item.erase(0, item.find_first_not_of(" \t\n\r\f\v"));
                item.erase(item.find_last_not_of(" \t\n\r\f\v") + 1);
                if (!item.empty()) {
                    data.push_back(std::stod(item));
                }
            } catch (const std::invalid_argument& ia) {
                std::cerr << "[ERROR] Invalid argument for std::stod: '" << item << "' in " << filename << std::endl;
            } catch (const std::out_of_range& oor) {
                std::cerr << "[ERROR] Out of range for std::stod: '" << item << "' in " << filename << std::endl;
            }
        }
        std::cout << "[INFO] Successfully loaded " << data.size() << " data points from " << filename << std::endl;
    } else {
        std::cerr << "[ERROR] Unable to open file: " << filename << std::endl;
    }
    return data;
}

// 创建数据集
void create_dataset(const std::vector<double>& data,
                    std::vector<double>& X,
                    std::vector<double>& y,
                    int input_dim) {
    if (data.size() < static_cast<size_t>(input_dim + 1)) {
        std::cerr << "[ERROR] Not enough data to create a dataset with input_dim " << input_dim << std::endl;
        return;
    }
    X.clear();
    y.clear();
    for (size_t i = 0; i < data.size() - input_dim; ++i) {
        for (int j = 0; j < input_dim; ++j) {
            X.push_back(data[i + j]);
        }
        y.push_back(data[i + input_dim]);
    }
}

// ReLU激活函数 (CPU)
void relu_forward_cpu(std::vector<double>& x) {
    for (auto& val : x) {
        if (val < 0) {
            val = 0;
        }
    }
}

// 全连接层前向传播 (CPU) - 主要用于概念展示，实际应在GPU上进行
void linear_forward_cpu(const std::vector<double>& input, 
                        const std::vector<double>& weights, 
                        const std::vector<double>& biases, 
                        std::vector<double>& output,
                        int input_features,
                        int output_features) {
    output.assign(output_features, 0.0);
    for (int i = 0; i < output_features; ++i) {
        for (int j = 0; j < input_features; ++j) {
            output[i] += input[j] * weights[i * input_features + j];
        }
        output[i] += biases[i];
    }
}

// 数据归一化处理
void normalize_data(std::vector<double>& data, double& min_val, double& max_val) {
    min_val = *std::min_element(data.begin(), data.end());
    max_val = *std::max_element(data.begin(), data.end());
    for (auto& val : data) {
        val = (val - min_val) / (max_val - min_val);
    }
    return;
}

// 数据反归一化处理
void denormalize_data(std::vector<double>& data, double min_val, double max_val) {
    for (auto& val : data) {
        val = val * (max_val - min_val) + min_val;
    }
    // return; // Redundant return
}

// Helper for HIP error checking
#define HIP_CHECK(error) \\
    do { \\
        hipError_t err = error; \\
        if (err != hipSuccess) { \\
            std::cerr << "[HIP ERROR] " << hipGetErrorString(err) << " in file " << __FILE__ \\
                      << " at line " << __LINE__ << std::endl; \\
            exit(EXIT_FAILURE); \\
        } \\
    } while (0)

__global__ void add_bias_kernel(double* output, const double* biases, int batch_size, int features_dim) {
    int batch_idx = blockIdx.y; // Assuming gridDim.y is batch_size
    int feature_idx = blockIdx.x * blockDim.x + threadIdx.x;

    if (batch_idx < batch_size && feature_idx < features_dim) {
        output[batch_idx * features_dim + feature_idx] += biases[feature_idx];
    }
}

__global__ void relu_forward_kernel(double* data, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        if (data[idx] < 0) {
            data[idx] = 0;
        }
    }
}

// Kernel to compute sum of gradients for biases (reduction over batch)
// This is a simplified version. A more optimized reduction would use shared memory.
__global__ void sum_bias_gradients_kernel(const double* grad_output_layer, double* grad_biases, int batch_size, int features_dim) {
    // Each block computes gradient for one bias term
    int feature_idx = blockIdx.x;
    if (feature_idx < features_dim) {
        double sum = 0.0;
        for (int i = threadIdx.x; i < batch_size; i += blockDim.x) {
            sum += grad_output_layer[i * features_dim + feature_idx];
        }
        // Atomic add or a more sophisticated reduction is needed if multiple threads per block write to the same grad_biases[feature_idx]
        // For this simple version, assuming one thread block per bias gradient and threads sum up parts.
        // This requires a final reduction step if blockDim.x > 1 for summing up partial sums from threads in a block.
        // For now, let's assume blockDim.x is reasonably small or 1 for this specific kernel call, or a single thread per block does the sum.
        // A more robust approach: each thread computes its partial sum, then reduce within block, then atomic add to global.
        // Simplified: one thread per bias, iterating over batch. Not performant but simple.
        if (threadIdx.x == 0) { // Only one thread per block writes
             atomicAdd(&grad_biases[feature_idx], sum); // Needs grad_biases to be zeroed before each batch accumulation if not using atomics directly in loop
        }
    }
}

__global__ void transpose_kernel(const double* input, double* output, int rows, int cols) {
    int ridx = blockIdx.y * blockDim.y + threadIdx.y;
    int cidx = blockIdx.x * blockDim.x + threadIdx.x;

    if (ridx < rows && cidx < cols) {
        output[cidx * rows + ridx] = input[ridx * cols + cidx];
    }
}

// ----------------------------- Main -------------------------------
int main() {
    // --- 1. 数据准备 ---
    std::vector<double> raw_bandwidth_data = load_json_bandwidth("starlink_bw.json"); 

    if (raw_bandwidth_data.empty()) {
        std::cout << "[INFO] No data loaded from JSON, using random data for demonstration." << std::endl;
        raw_bandwidth_data.resize(1000 + INPUT_DIM); // Ensure enough for at least one sample
        for(size_t i = 0; i < raw_bandwidth_data.size(); ++i) {
            raw_bandwidth_data[i] = static_cast<double>(rand() % 1000) / 10.0;
        }
    }

    double min_val, max_val;
    normalize_data(raw_bandwidth_data, min_val, max_val);

    std::vector<double> h_X_full, h_y_full; 
    create_dataset(raw_bandwidth_data, h_X_full, h_y_full, INPUT_DIM);

    if (h_X_full.empty() || h_y_full.empty()) {
        std::cerr << "[ERROR] Failed to create dataset. Exiting." << std::endl;
        return 1;
    }
    
    size_t total_samples = h_y_full.size();
    if (total_samples == 0) {
        std::cerr << "[ERROR] Dataset is empty after creation. Check data and INPUT_DIM. Exiting." << std::endl;
        return 1;
    }
    std::cout << "[INFO] Total samples: " << total_samples << std::endl;
    std::cout << "[INFO] Input dimension: " << INPUT_DIM << ", Hidden dimension: " << HIDDEN_DIM << ", Output dimension: " << OUTPUT_DIM << std::endl;
    std::cout << "[INFO] Batch size: " << BATCH_SIZE << ", Epochs: " << EPOCHS << ", Learning rate: " << LEARNING_RATE << std::endl;


    // --- 2. MLP权重和偏置初始化 (CPU) ---
    std::vector<double> h_W1(INPUT_DIM * HIDDEN_DIM);
    std::vector<double> h_b1(HIDDEN_DIM);
    std::vector<double> h_W2(HIDDEN_DIM * OUTPUT_DIM);
    std::vector<double> h_b2(OUTPUT_DIM);

    srand(static_cast<unsigned int>(time(0)));
    auto init_weights = [&](std::vector<double>& weights, int in_dim, int out_dim) {
        double limit = std::sqrt(6.0 / (in_dim + out_dim));
        for (auto& w : weights) {
            w = (static_cast<double>(rand()) / RAND_MAX) * 2 * limit - limit;
        }
    };
    init_weights(h_W1, INPUT_DIM, HIDDEN_DIM);
    init_weights(h_W2, HIDDEN_DIM, OUTPUT_DIM);
    std::fill(h_b1.begin(), h_b1.end(), 0.0);
    std::fill(h_b2.begin(), h_b2.end(), 0.0);

    // --- 3. GPU内存分配与数据拷贝 ---
    double *d_W1, *d_b1, *d_W2, *d_b2;
    double *d_X_batch, *d_y_batch;
    double *d_h1_out, *d_h1_act; 
    double *d_out_final;      
    double *d_loss_device;    

    HIP_CHECK(hipMalloc(&d_W1, h_W1.size() * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_b1, h_b1.size() * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_W2, h_W2.size() * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_b2, h_b2.size() * sizeof(double)));

    // Determine max batch size for allocations if BATCH_SIZE is flexible, or use BATCH_SIZE directly
    size_t effective_batch_size_alloc = BATCH_SIZE; 

    HIP_CHECK(hipMalloc(&d_X_batch, effective_batch_size_alloc * INPUT_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_y_batch, effective_batch_size_alloc * OUTPUT_DIM * sizeof(double)));
    
    HIP_CHECK(hipMalloc(&d_h1_out, effective_batch_size_alloc * HIDDEN_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_h1_act, effective_batch_size_alloc * HIDDEN_DIM * sizeof(double))); 
    HIP_CHECK(hipMalloc(&d_out_final, effective_batch_size_alloc * OUTPUT_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_loss_device, sizeof(double)));

    HIP_CHECK(hipMemcpy(d_W1, h_W1.data(), h_W1.size() * sizeof(double), hipMemcpyHostToDevice));
    HIP_CHECK(hipMemcpy(d_b1, h_b1.data(), h_b1.size() * sizeof(double), hipMemcpyHostToDevice));
    HIP_CHECK(hipMemcpy(d_W2, h_W2.data(), h_W2.size() * sizeof(double), hipMemcpyHostToDevice));
    HIP_CHECK(hipMemcpy(d_b2, h_b2.data(), h_b2.size() * sizeof(double), hipMemcpyHostToDevice));

    double *d_grad_W1, *d_grad_b1, *d_grad_W2, *d_grad_b2;
    double *d_grad_out_final, *d_grad_h1_act, *d_grad_h1_out;
    double *d_X_batch_T, *d_h1_act_T; // For transposed matrices in gradient calculation

    HIP_CHECK(hipMalloc(&d_grad_W1, INPUT_DIM * HIDDEN_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_grad_b1, HIDDEN_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_grad_W2, HIDDEN_DIM * OUTPUT_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_grad_b2, OUTPUT_DIM * sizeof(double)));
    
    HIP_CHECK(hipMalloc(&d_grad_out_final, effective_batch_size_alloc * OUTPUT_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_grad_h1_act, effective_batch_size_alloc * HIDDEN_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_grad_h1_out, effective_batch_size_alloc * HIDDEN_DIM * sizeof(double)));

    HIP_CHECK(hipMalloc(&d_X_batch_T, effective_batch_size_alloc * INPUT_DIM * sizeof(double))); // Transposed: (INPUT_DIM, current_batch_size)
    HIP_CHECK(hipMalloc(&d_h1_act_T, effective_batch_size_alloc * HIDDEN_DIM * sizeof(double))); // Transposed: (HIDDEN_DIM, current_batch_size)

    // Temporary device storage for transposed weights, if needed for backprop
    double *d_W1_T, *d_W2_T;
    HIP_CHECK(hipMalloc(&d_W1_T, INPUT_DIM * HIDDEN_DIM * sizeof(double))); // (HIDDEN_DIM, INPUT_DIM)
    HIP_CHECK(hipMalloc(&d_W2_T, HIDDEN_DIM * OUTPUT_DIM * sizeof(double))); // (OUTPUT_DIM, HIDDEN_DIM)

    dim3 block_dim_matmul(16, 16); 
    dim3 block_dim_elementwise(256);

    std::cout << "[INFO] Starting training for " << EPOCHS << " epochs..." << std::endl;
    
    for (int epoch = 0; epoch < EPOCHS; ++epoch) {
        auto epoch_start_time = std::chrono::high_resolution_clock::now();
        double accumulated_epoch_loss = 0.0;
        int num_batches = (total_samples + BATCH_SIZE - 1) / BATCH_SIZE;
        if (num_batches == 0 && total_samples > 0) num_batches = 1; // Handle case where total_samples < BATCH_SIZE

        for (int batch_idx = 0; batch_idx < num_batches; ++batch_idx) {
            int current_batch_size = BATCH_SIZE;
            int offset = batch_idx * BATCH_SIZE;
            if (offset + current_batch_size > total_samples) {
                current_batch_size = total_samples - offset;
            }
            if (current_batch_size <= 0) continue;
            if (static_cast<size_t>(offset * INPUT_DIM + current_batch_size * INPUT_DIM) > h_X_full.size() ||
                static_cast<size_t>(offset * OUTPUT_DIM + current_batch_size * OUTPUT_DIM) > h_y_full.size()) {
                 std::cerr << "[ERROR] Batch indexing out of bounds. Offset: " << offset 
                           << ", current_batch_size: " << current_batch_size 
                           << ", total_samples: " << total_samples << std::endl;
                 // Potentially skip this batch or handle error appropriately
                 continue; 
            }

            HIP_CHECK(hipMemcpy(d_X_batch, h_X_full.data() + offset * INPUT_DIM, current_batch_size * INPUT_DIM * sizeof(double), hipMemcpyHostToDevice));
            HIP_CHECK(hipMemcpy(d_y_batch, h_y_full.data() + offset * OUTPUT_DIM, current_batch_size * OUTPUT_DIM * sizeof(double), hipMemcpyHostToDevice));
            
            HIP_CHECK(hipMemset(d_loss_device, 0, sizeof(double)));
            HIP_CHECK(hipMemset(d_grad_b1, 0, HIDDEN_DIM * sizeof(double))); // Zero bias gradients for accumulation
            HIP_CHECK(hipMemset(d_grad_b2, 0, OUTPUT_DIM * sizeof(double)));

            // --- 前向传播 ---
            // Layer 1: h1_out = X_batch * W1
            dim3 grid_matmul_l1((HIDDEN_DIM + block_dim_matmul.x - 1) / block_dim_matmul.x, (current_batch_size + block_dim_matmul.y - 1) / block_dim_matmul.y);
            hipLaunchKernelGGL(matmul, grid_matmul_l1, block_dim_matmul, 0, 0, d_X_batch, d_W1, d_h1_out, current_batch_size, INPUT_DIM, HIDDEN_DIM);
            HIP_CHECK(hipGetLastError());

            // Add bias b1: h1_out += b1
            dim3 grid_add_bias1((HIDDEN_DIM + block_dim_elementwise.x - 1) / block_dim_elementwise.x, current_batch_size); // grid.y = batch_size, grid.x for features
            hipLaunchKernelGGL(add_bias_kernel, grid_add_bias1, dim3(block_dim_elementwise.x), 0, 0, d_h1_out, d_b1, current_batch_size, HIDDEN_DIM);
            HIP_CHECK(hipGetLastError());

            // ReLU activation: h1_act = ReLU(h1_out)
            HIP_CHECK(hipMemcpy(d_h1_act, d_h1_out, current_batch_size * HIDDEN_DIM * sizeof(double), hipMemcpyDeviceToDevice));
            dim3 grid_relu1(((current_batch_size * HIDDEN_DIM) + block_dim_elementwise.x - 1) / block_dim_elementwise.x);
            hipLaunchKernelGGL(relu_forward_kernel, grid_relu1, block_dim_elementwise, 0, 0, d_h1_act, current_batch_size * HIDDEN_DIM);
            HIP_CHECK(hipGetLastError());

            // Layer 2: out_final = h1_act * W2
            dim3 grid_matmul_l2((OUTPUT_DIM + block_dim_matmul.x - 1) / block_dim_matmul.x, (current_batch_size + block_dim_matmul.y - 1) / block_dim_matmul.y);
            hipLaunchKernelGGL(matmul, grid_matmul_l2, block_dim_matmul, 0, 0, d_h1_act, d_W2, d_out_final, current_batch_size, HIDDEN_DIM, OUTPUT_DIM);
            HIP_CHECK(hipGetLastError());

            // Add bias b2: out_final += b2
            dim3 grid_add_bias2((OUTPUT_DIM + block_dim_elementwise.x - 1) / block_dim_elementwise.x, current_batch_size);
            hipLaunchKernelGGL(add_bias_kernel, grid_add_bias2, dim3(block_dim_elementwise.x), 0, 0, d_out_final, d_b2, current_batch_size, OUTPUT_DIM);
            HIP_CHECK(hipGetLastError());

            // --- 计算损失 ---
            dim3 grid_loss(((current_batch_size * OUTPUT_DIM) + block_dim_elementwise.x - 1) / block_dim_elementwise.x);
            hipLaunchKernelGGL(compute_mse_loss, grid_loss, block_dim_elementwise, 0, 0, d_out_final, d_y_batch, d_loss_device, current_batch_size, OUTPUT_DIM);
            HIP_CHECK(hipGetLastError());
            
            double h_batch_loss_sum;
            HIP_CHECK(hipMemcpy(&h_batch_loss_sum, d_loss_device, sizeof(double), hipMemcpyDeviceToHost));
            accumulated_epoch_loss += h_batch_loss_sum; // Sum of squared errors, will average at end of epoch

            // --- 反向传播 ---
            // 1. Gradient of loss w.r.t. final output
            dim3 grid_grad_out(((current_batch_size * OUTPUT_DIM) + block_dim_elementwise.x - 1) / block_dim_elementwise.x);
            hipLaunchKernelGGL(compute_output_grad, grid_grad_out, block_dim_elementwise, 0, 0, d_out_final, d_y_batch, d_grad_out_final, current_batch_size * OUTPUT_DIM);
            HIP_CHECK(hipGetLastError());
            // Normalize output grad by batch_size * output_dim (can also be done in learning rate)
            // For now, this normalization is effectively done when averaging total loss.

            // 2. Gradient for W2 and b2
            // d_grad_W2 = h1_act^T * d_grad_out_final
            // Transpose h1_act: d_h1_act(current_batch_size, HIDDEN_DIM) -> d_h1_act_T(HIDDEN_DIM, current_batch_size)
            dim3 grid_transpose_h1_act((HIDDEN_DIM + block_dim_matmul.x -1)/block_dim_matmul.x, (current_batch_size + block_dim_matmul.y -1)/block_dim_matmul.y);
            hipLaunchKernelGGL(transpose_kernel, grid_transpose_h1_act, block_dim_matmul, 0, 0, d_h1_act, d_h1_act_T, current_batch_size, HIDDEN_DIM);
            HIP_CHECK(hipGetLastError());
            // d_grad_W2(HIDDEN_DIM, OUTPUT_DIM) = d_h1_act_T(HIDDEN_DIM, current_batch_size) * d_grad_out_final(current_batch_size, OUTPUT_DIM)
            dim3 grid_grad_W2((OUTPUT_DIM + block_dim_matmul.x - 1)/block_dim_matmul.x, (HIDDEN_DIM + block_dim_matmul.y -1)/block_dim_matmul.y);
            hipLaunchKernelGGL(matmul, grid_grad_W2, block_dim_matmul, 0, 0, d_h1_act_T, d_grad_out_final, d_grad_W2, HIDDEN_DIM, current_batch_size, OUTPUT_DIM);
            HIP_CHECK(hipGetLastError());

            // d_grad_b2 = sum(d_grad_out_final, axis=0) (sum over batch dimension)
            dim3 grid_sum_bias_grad2(OUTPUT_DIM); // One block per bias term
            dim3 block_sum_bias_grad2(256); // Threads in block cooperate for sum if batch_size > 256
            hipLaunchKernelGGL(sum_bias_gradients_kernel, grid_sum_bias_grad2, block_sum_bias_grad2, 0, 0, d_grad_out_final, d_grad_b2, current_batch_size, OUTPUT_DIM);
            HIP_CHECK(hipGetLastError());

            // 3. Gradient w.r.t. h1_act: d_grad_h1_act = d_grad_out_final * W2^T
            // Transpose W2: d_W2(HIDDEN_DIM, OUTPUT_DIM) -> d_W2_T(OUTPUT_DIM, HIDDEN_DIM)
            dim3 grid_transpose_W2((HIDDEN_DIM + block_dim_matmul.x - 1)/block_dim_matmul.x, (OUTPUT_DIM + block_dim_matmul.y - 1)/block_dim_matmul.y);
            hipLaunchKernelGGL(transpose_kernel, grid_transpose_W2, block_dim_matmul, 0, 0, d_W2, d_W2_T, HIDDEN_DIM, OUTPUT_DIM);
            HIP_CHECK(hipGetLastError());
            // d_grad_h1_act(current_batch_size, HIDDEN_DIM) = d_grad_out_final(current_batch_size, OUTPUT_DIM) * d_W2_T(OUTPUT_DIM, HIDDEN_DIM)
            dim3 grid_grad_h1_act((HIDDEN_DIM + block_dim_matmul.x - 1)/block_dim_matmul.x, (current_batch_size + block_dim_matmul.y - 1)/block_dim_matmul.y);
            hipLaunchKernelGGL(matmul, grid_grad_h1_act, block_dim_matmul, 0, 0, d_grad_out_final, d_W2_T, d_grad_h1_act, current_batch_size, OUTPUT_DIM, HIDDEN_DIM);
            HIP_CHECK(hipGetLastError());

            // 4. Gradient through ReLU: d_grad_h1_out = d_grad_h1_act * (h1_act > 0)
            HIP_CHECK(hipMemcpy(d_grad_h1_out, d_grad_h1_act, current_batch_size * HIDDEN_DIM * sizeof(double), hipMemcpyDeviceToDevice));
            dim3 grid_relu_bw(((current_batch_size*HIDDEN_DIM) + block_dim_elementwise.x -1) / block_dim_elementwise.x);
            hipLaunchKernelGGL(compute_relu_backward, grid_relu_bw, block_dim_elementwise, 0, 0, d_grad_h1_out, d_h1_act, current_batch_size * HIDDEN_DIM);
            HIP_CHECK(hipGetLastError());

            // 5. Gradient for W1 and b1
            // d_grad_W1 = X_batch^T * d_grad_h1_out
            // Transpose X_batch: d_X_batch(current_batch_size, INPUT_DIM) -> d_X_batch_T(INPUT_DIM, current_batch_size)
            dim3 grid_transpose_X_batch((INPUT_DIM + block_dim_matmul.x -1)/block_dim_matmul.x, (current_batch_size + block_dim_matmul.y -1)/block_dim_matmul.y);
            hipLaunchKernelGGL(transpose_kernel, grid_transpose_X_batch, block_dim_matmul, 0, 0, d_X_batch, d_X_batch_T, current_batch_size, INPUT_DIM);
            HIP_CHECK(hipGetLastError());
            // d_grad_W1(INPUT_DIM, HIDDEN_DIM) = d_X_batch_T(INPUT_DIM, current_batch_size) * d_grad_h1_out(current_batch_size, HIDDEN_DIM)
            dim3 grid_grad_W1((HIDDEN_DIM + block_dim_matmul.x -1)/block_dim_matmul.x, (INPUT_DIM + block_dim_matmul.y -1)/block_dim_matmul.y);
            hipLaunchKernelGGL(matmul, grid_grad_W1, block_dim_matmul, 0, 0, d_X_batch_T, d_grad_h1_out, d_grad_W1, INPUT_DIM, current_batch_size, HIDDEN_DIM);
            HIP_CHECK(hipGetLastError());
            
            // d_grad_b1 = sum(d_grad_h1_out, axis=0)
            dim3 grid_sum_bias_grad1(HIDDEN_DIM); 
            dim3 block_sum_bias_grad1(256);
            hipLaunchKernelGGL(sum_bias_gradients_kernel, grid_sum_bias_grad1, block_sum_bias_grad1, 0, 0, d_grad_h1_out, d_grad_b1, current_batch_size, HIDDEN_DIM);
            HIP_CHECK(hipGetLastError());

            // --- 参数更新 (SGD) ---
            // Normalize gradients by batch size (can also be part of learning rate)
            // For weights: grad /= current_batch_size ; For biases: grad /= current_batch_size
            // This is a critical step. Or adjust LEARNING_RATE to LEARNING_RATE / current_batch_size
            double effective_lr = LEARNING_RATE / current_batch_size;

            dim3 grid_sgd_w1(((INPUT_DIM * HIDDEN_DIM) + block_dim_elementwise.x -1) / block_dim_elementwise.x);
            hipLaunchKernelGGL(sgd_update, grid_sgd_w1, block_dim_elementwise, 0, 0, d_W1, d_grad_W1, effective_lr, INPUT_DIM * HIDDEN_DIM);
            HIP_CHECK(hipGetLastError());

            dim3 grid_sgd_b1(((HIDDEN_DIM) + block_dim_elementwise.x -1)/block_dim_elementwise.x);
            hipLaunchKernelGGL(sgd_update, grid_sgd_b1, block_dim_elementwise, 0, 0, d_b1, d_grad_b1, effective_lr, HIDDEN_DIM);
            HIP_CHECK(hipGetLastError());
            
            dim3 grid_sgd_w2(((HIDDEN_DIM * OUTPUT_DIM) + block_dim_elementwise.x -1)/block_dim_elementwise.x);
            hipLaunchKernelGGL(sgd_update, grid_sgd_w2, block_dim_elementwise, 0, 0, d_W2, d_grad_W2, effective_lr, HIDDEN_DIM * OUTPUT_DIM);
            HIP_CHECK(hipGetLastError());

            dim3 grid_sgd_b2(((OUTPUT_DIM) + block_dim_elementwise.x -1)/block_dim_elementwise.x);
            hipLaunchKernelGGL(sgd_update, grid_sgd_b2, block_dim_elementwise, 0, 0, d_b2, d_grad_b2, effective_lr, OUTPUT_DIM);
            HIP_CHECK(hipGetLastError());
        }
        HIP_CHECK(hipDeviceSynchronize());

        auto epoch_end_time = std::chrono::high_resolution_clock::now();
        auto epoch_duration = std::chrono::duration_cast<std::chrono::milliseconds>(epoch_end_time - epoch_start_time);
        
        double average_epoch_loss = accumulated_epoch_loss / (total_samples * OUTPUT_DIM) ; // Average MSE over all samples and outputs
        std::cout << "[Epoch " << epoch + 1 << "/" << EPOCHS << "] Loss: " << average_epoch_loss 
                  << ", Time: " << epoch_duration.count() << " ms" << std::endl;
    }

    // --- 5. 推理部分 ---
    std::cout << "[INFO] Starting inference..." << std::endl;
    // Weights (d_W1, d_b1, d_W2, d_b2) are already on the device and trained.

    int num_inference_samples = std::min(static_cast<int>(total_samples), 20); // Predict on a few samples
    if (num_inference_samples <=0) {
        std::cout << "[INFO] No samples to run inference on." << std::endl;
    } else {
        std::vector<double> h_X_infer(num_inference_samples * INPUT_DIM);
        std::vector<double> h_y_actual_infer_denorm(num_inference_samples * OUTPUT_DIM);
        
        // Prepare inference data (first num_inference_samples from the dataset)
        memcpy(h_X_infer.data(), h_X_full.data(), num_inference_samples * INPUT_DIM * sizeof(double));
        // We need the original y values for comparison, denormalized
        std::vector<double> temp_y_actual_norm(num_inference_samples * OUTPUT_DIM);
        memcpy(temp_y_actual_norm.data(), h_y_full.data(), num_inference_samples * OUTPUT_DIM * sizeof(double));
        h_y_actual_infer_denorm = temp_y_actual_norm;
        denormalize_data(h_y_actual_infer_denorm, min_val, max_val); 

        // GPU memory for inference batch (can reuse/reallocate if sizes differ significantly from training)
        double *d_X_infer, *d_h1_out_infer, *d_h1_act_infer, *d_out_final_infer;
        HIP_CHECK(hipMalloc(&d_X_infer, num_inference_samples * INPUT_DIM * sizeof(double)));
        HIP_CHECK(hipMalloc(&d_h1_out_infer, num_inference_samples * HIDDEN_DIM * sizeof(double)));
        HIP_CHECK(hipMalloc(&d_h1_act_infer, num_inference_samples * HIDDEN_DIM * sizeof(double)));
        HIP_CHECK(hipMalloc(&d_out_final_infer, num_inference_samples * OUTPUT_DIM * sizeof(double)));

        HIP_CHECK(hipMemcpy(d_X_infer, h_X_infer.data(), num_inference_samples * INPUT_DIM * sizeof(double), hipMemcpyHostToDevice));

        // Inference Forward Pass
        // Layer 1: matmul
        dim3 grid_infer_l1((HIDDEN_DIM + block_dim_matmul.x - 1) / block_dim_matmul.x, (num_inference_samples + block_dim_matmul.y - 1) / block_dim_matmul.y);
        hipLaunchKernelGGL(matmul, grid_infer_l1, block_dim_matmul, 0, 0, d_X_infer, d_W1, d_h1_out_infer, num_inference_samples, INPUT_DIM, HIDDEN_DIM);
        HIP_CHECK(hipGetLastError());
        // Add bias b1
        dim3 grid_add_bias_infer1((HIDDEN_DIM + block_dim_elementwise.x - 1) / block_dim_elementwise.x, num_inference_samples);
        hipLaunchKernelGGL(add_bias_kernel, grid_add_bias_infer1, dim3(block_dim_elementwise.x), 0, 0, d_h1_out_infer, d_b1, num_inference_samples, HIDDEN_DIM);
        HIP_CHECK(hipGetLastError());
        // ReLU
        HIP_CHECK(hipMemcpy(d_h1_act_infer, d_h1_out_infer, num_inference_samples * HIDDEN_DIM * sizeof(double), hipMemcpyDeviceToDevice));
        dim3 grid_relu_infer1(((num_inference_samples * HIDDEN_DIM) + block_dim_elementwise.x - 1) / block_dim_elementwise.x);
        hipLaunchKernelGGL(relu_forward_kernel, grid_relu_infer1, block_dim_elementwise, 0, 0, d_h1_act_infer, num_inference_samples * HIDDEN_DIM);
        HIP_CHECK(hipGetLastError());

        // Layer 2: matmul
        dim3 grid_infer_l2((OUTPUT_DIM + block_dim_matmul.x - 1) / block_dim_matmul.x, (num_inference_samples + block_dim_matmul.y - 1) / block_dim_matmul.y);
        hipLaunchKernelGGL(matmul, grid_infer_l2, block_dim_matmul, 0, 0, d_h1_act_infer, d_W2, d_out_final_infer, num_inference_samples, HIDDEN_DIM, OUTPUT_DIM);
        HIP_CHECK(hipGetLastError());
        // Add bias b2
        dim3 grid_add_bias_infer2((OUTPUT_DIM + block_dim_elementwise.x - 1) / block_dim_elementwise.x, num_inference_samples);
        hipLaunchKernelGGL(add_bias_kernel, grid_add_bias_infer2, dim3(block_dim_elementwise.x), 0, 0, d_out_final_infer, d_b2, num_inference_samples, OUTPUT_DIM);
        HIP_CHECK(hipGetLastError());

        std::vector<double> h_predictions_norm(num_inference_samples * OUTPUT_DIM);
        HIP_CHECK(hipMemcpy(h_predictions_norm.data(), d_out_final_infer, num_inference_samples * OUTPUT_DIM * sizeof(double), hipMemcpyDeviceToHost));
        
        std::vector<double> h_predictions_denorm = h_predictions_norm;
        denormalize_data(h_predictions_denorm, min_val, max_val);

        double total_squared_error_infer = 0;
        std::cout << "[INFO] Predictions vs Actuals (denormalized) for first " << num_inference_samples << " samples:" << std::endl;
        for (int i = 0; i < num_inference_samples; ++i) {
            for(int j=0; j < OUTPUT_DIM; ++j) {
                int idx = i * OUTPUT_DIM + j;
                std::cout << "  Sample " << i << ", Output " << j << ": Predicted=" << h_predictions_denorm[idx] 
                          << ", Actual=" << h_y_actual_infer_denorm[idx] << std::endl;
                double error = h_predictions_denorm[idx] - h_y_actual_infer_denorm[idx];
                total_squared_error_infer += error * error;
            }
        }
        double mse_infer = total_squared_error_infer / (num_inference_samples * OUTPUT_DIM);
        std::cout << "[INFO] Mean Squared Error (MSE) on these inference samples: " << mse_infer << std::endl;

        HIP_CHECK(hipFree(d_X_infer)); 
        HIP_CHECK(hipFree(d_h1_out_infer));
        HIP_CHECK(hipFree(d_h1_act_infer)); 
        HIP_CHECK(hipFree(d_out_final_infer));
    }
    
    // --- 6. 资源清理 ---
    HIP_CHECK(hipFree(d_W1)); HIP_CHECK(hipFree(d_b1));
    HIP_CHECK(hipFree(d_W2)); HIP_CHECK(hipFree(d_b2));
    HIP_CHECK(hipFree(d_X_batch)); HIP_CHECK(hipFree(d_y_batch));
    HIP_CHECK(hipFree(d_h1_out)); HIP_CHECK(hipFree(d_h1_act));
    HIP_CHECK(hipFree(d_out_final)); HIP_CHECK(hipFree(d_loss_device));
    HIP_CHECK(hipFree(d_grad_W1)); HIP_CHECK(hipFree(d_grad_b1));
    HIP_CHECK(hipFree(d_grad_W2)); HIP_CHECK(hipFree(d_grad_b2));
    HIP_CHECK(hipFree(d_grad_out_final)); HIP_CHECK(hipFree(d_grad_h1_act)); HIP_CHECK(hipFree(d_grad_h1_out));
    HIP_CHECK(hipFree(d_X_batch_T)); HIP_CHECK(hipFree(d_h1_act_T));
    HIP_CHECK(hipFree(d_W1_T)); HIP_CHECK(hipFree(d_W2_T));

    std::cout << "[INFO] MLP execution finished." << std::endl;
    return 0;
}

/*
// 编译文件
// hipcc main_3.cc -o mlp_app -O3 $(hipconfig --cpp_config)
// (或者: hipcc main_3.cc -o mlp_app -O3 `hipconfig --cxxflags` `hipconfig --ldflags`)

// 执行文件
// ./mlp_app
// 或者 hipprof ./mlp_app

// 注意事项:
// 1. JSON加载: `load_json_bandwidth` 函数需要您根据实际的 `bandwidth_data.json` 文件格式进行具体实现。
//    目前它在文件不存在或为空时使用随机数据。
// 2. 性能:
//    - `matmul`, `transpose_kernel`, `sum_bias_gradients_kernel` 是基础实现。为了达到非常好的性能，
//      可以考虑使用 rocBLAS/hipBLAS 库中的 GEMM 操作，它针对矩阵乘法有高度优化。
//    - 线程块和网格维度 (`block_dim_matmul`, `block_dim_elementwise`, etc.) 可能需要根据您的具体GPU硬件进行调整以获得最佳性能。
//    - 共享内存未在这些自定义内核中使用，使用共享内存可以显著提高 `matmul` 和 `transpose_kernel` 的性能。
//    - `sum_bias_gradients_kernel` 中的reduce操作非常简化，更高效的reduce需要多级并行和共享内存。
// 3. 数值稳定性: 对于深度网络或特定数据集，可能需要更仔细的权重初始化策略或梯度裁剪等技术。
// 4. 错误处理: `HIP_CHECK` 宏提供了基本的HIP运行时错误检查。
// 5. 扩展性: 
//    - 添加更多层或不同类型的激活函数需要扩展前向和反向传播逻辑。
//    - 支持不同的优化器（如Adam）需要实现相应的更新规则。
//    - 模型保存/加载功能可以添加，以便持久化训练好的模型状态。
// 6. 数据类型: 当前代码使用 `double`。对于某些GPU，`float` 类型可能会提供更好的性能，但可能会牺牲一些精度。
//    如果切换到 `float`，所有 `sizeof(double)` 和相关向量/指针类型都需要更新。
*/

