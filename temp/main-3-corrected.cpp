// 修正版本的loss计算

// 选项1：使用标准MSE（不带0.5系数）
__global__ void compute_mse_loss_standard(const double* pred, const double* target, double* loss, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        double diff = pred[idx] - target[idx];
        loss[idx] = diff * diff;  // 标准MSE，不带0.5
    }
}

// 对应的梯度计算需要乘以2
__global__ void compute_output_grad_standard(const double* pred, const double* target, double* grad, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        grad[idx] = 2.0 * (pred[idx] - target[idx]);  // 标准MSE的梯度
    }
}

// 选项2：保持0.5系数但明确说明
__global__ void compute_mse_loss_half(const double* pred, const double* target, double* loss, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        double diff = pred[idx] - target[idx];
        loss[idx] = 0.5 * diff * diff;  // 用于简化梯度的MSE
    }
}

// 对应的梯度计算保持不变
__global__ void compute_output_grad_half(const double* pred, const double* target, double* grad, int size) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if (idx < size) {
        grad[idx] = pred[idx] - target[idx];  // 0.5*MSE的梯度
    }
}

// 修正训练循环中的loss累积逻辑
for (int batch_idx = 0; batch_idx < num_batches; ++batch_idx) {
    // ... 前向传播代码 ...
    
    // --- Loss Calculation ---
    dim3 gridDim_loss(((current_batch_size * OUTPUT_DIM) + threadsPerBlock_1D.x -1) / threadsPerBlock_1D.x);
    compute_mse_loss<<<gridDim_loss, threadsPerBlock_1D>>>(d_output, d_batch_y, d_loss_array, current_batch_size * OUTPUT_DIM);
    HIP_CHECK(hipGetLastError());
    
    // 修正：正确计算batch loss
    std::vector<double> h_batch_loss(current_batch_size * OUTPUT_DIM);
    HIP_CHECK(hipMemcpy(h_batch_loss.data(), d_loss_array, current_batch_size * OUTPUT_DIM * sizeof(double), hipMemcpyDeviceToHost));
    
    double current_batch_loss = 0.0;
    for(double l : h_batch_loss) {
        current_batch_loss += l;  // 当前batch的总loss
    }
    total_epoch_loss += current_batch_loss;  // 累积到epoch总loss
    
    // ... 反向传播代码 ...
}

// 修正：loss平均化
// 如果使用0.5系数的MSE
double average_epoch_loss = total_epoch_loss / train_samples;  // 每个样本的平均loss

// 如果要报告标准MSE（不带0.5）
double standard_mse = (total_epoch_loss * 2.0) / train_samples;  // 转换为标准MSE