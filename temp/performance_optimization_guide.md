# HIP 矩阵乘法性能优化指南

## 概述

基于您的 MLP 代码 (`main-3.cpp`)，以下是针对 HIP GPU 的矩阵乘法性能优化策略。这些优化技术可以显著提高您的神经网络计算性能。

## 1. 共享内存优化 (Shared Memory Optimization)

### 1.1 分块矩阵乘法（Tiled Matrix Multiplication）

当前代码中的 `matmul` 函数性能较差，因为直接从全局内存读取数据。使用共享内存分块可以显著提升性能：

```cpp
/**
 * @brief 优化的分块矩阵乘法内核
 * 
 * 使用共享内存减少全局内存访问，提高内存带宽利用率
 * 
 * @param A 输入矩阵A (M x K)
 * @param B 输入矩阵B (K x N) 
 * @param C 输出矩阵C (M x N)
 * @param M 矩阵A的行数
 * @param N 矩阵B的列数
 * @param K 内积维度
 */
#define TILE_SIZE 32
__global__ void optimized_matmul_tiled(
    const double* __restrict__ A,
    const double* __restrict__ B, 
    double* __restrict__ C,
    int M, int N, int K) {
    
    // 共享内存声明 - 关键优化点1
    __shared__ double tile_A[TILE_SIZE][TILE_SIZE + 1]; // +1避免bank conflicts
    __shared__ double tile_B[TILE_SIZE][TILE_SIZE + 1];
    
    int tx = threadIdx.x;
    int ty = threadIdx.y;
    int bx = blockIdx.x;
    int by = blockIdx.y;
    
    int row = by * TILE_SIZE + ty;
    int col = bx * TILE_SIZE + tx;
    
    double sum = 0.0;
    
    // 分块循环 - 关键优化点2
    for (int tile = 0; tile < (K + TILE_SIZE - 1) / TILE_SIZE; ++tile) {
        // 协作加载瓦片到共享内存
        if (row < M && (tile * TILE_SIZE + tx) < K) {
            tile_A[ty][tx] = A[row * K + tile * TILE_SIZE + tx];
        } else {
            tile_A[ty][tx] = 0.0;
        }
        
        if (col < N && (tile * TILE_SIZE + ty) < K) {
            tile_B[ty][tx] = B[(tile * TILE_SIZE + ty) * N + col];
        } else {
            tile_B[ty][tx] = 0.0;
        }
        
        __syncthreads(); // 确保数据加载完成
        
        // 计算当前瓦片的贡献 - 关键优化点3
        #pragma unroll
        for (int k = 0; k < TILE_SIZE; ++k) {
            sum += tile_A[ty][k] * tile_B[k][tx];
        }
        
        __syncthreads(); // 准备下一次迭代
    }
    
    // 写回结果
    if (row < M && col < N) {
        C[row * N + col] = sum;
    }
}
```

### 1.2 避免共享内存Bank冲突

```cpp
// 关键优化：在共享内存声明中增加填充
__shared__ double tile_A[TILE_SIZE][TILE_SIZE + 1]; // +1 避免bank conflicts
__shared__ double tile_B[TILE_SIZE][TILE_SIZE + 1];
```

## 2. 寄存器分块优化 (Register Tiling)

### 2.1 每线程计算多个元素

```cpp
/**
 * @brief 寄存器分块优化的矩阵乘法
 * 
 * 每个线程计算输出矩阵的一个小块，提高计算密度
 */
#define TILE_SIZE 32
#define REG_TILE_M 4  // 每线程处理的行数
#define REG_TILE_N 4  // 每线程处理的列数

__global__ void register_tiled_matmul(
    const double* __restrict__ A,
    const double* __restrict__ B,
    double* __restrict__ C,
    int M, int N, int K) {
    
    __shared__ double tile_A[TILE_SIZE][TILE_SIZE + 1];
    __shared__ double tile_B[TILE_SIZE][TILE_SIZE + 1];
    
    int tx = threadIdx.x;
    int ty = threadIdx.y;
    
    // 每个线程负责计算 REG_TILE_M x REG_TILE_N 的输出块
    int base_row = blockIdx.y * TILE_SIZE + ty * REG_TILE_M;
    int base_col = blockIdx.x * TILE_SIZE + tx * REG_TILE_N;
    
    // 寄存器数组存储中间结果
    double reg_C[REG_TILE_M][REG_TILE_N] = {0.0};
    
    for (int tile = 0; tile < (K + TILE_SIZE - 1) / TILE_SIZE; ++tile) {
        // 加载数据到共享内存（每线程加载多个元素）
        #pragma unroll
        for (int i = 0; i < REG_TILE_M; ++i) {
            int row = base_row + i;
            if (row < M && (tile * TILE_SIZE + tx) < K) {
                tile_A[ty * REG_TILE_M + i][tx] = A[row * K + tile * TILE_SIZE + tx];
            }
        }
        
        #pragma unroll
        for (int j = 0; j < REG_TILE_N; ++j) {
            int col = base_col + j;
            if (col < N && (tile * TILE_SIZE + ty) < K) {
                tile_B[ty][tx * REG_TILE_N + j] = B[(tile * TILE_SIZE + ty) * N + col];
            }
        }
        
        __syncthreads();
        
        // 计算寄存器瓦片
        #pragma unroll
        for (int k = 0; k < TILE_SIZE; ++k) {
            #pragma unroll
            for (int i = 0; i < REG_TILE_M; ++i) {
                #pragma unroll
                for (int j = 0; j < REG_TILE_N; ++j) {
                    reg_C[i][j] += tile_A[ty * REG_TILE_M + i][k] * 
                                   tile_B[k][tx * REG_TILE_N + j];
                }
            }
        }
        
        __syncthreads();
    }
    
    // 写回寄存器结果
    #pragma unroll
    for (int i = 0; i < REG_TILE_M; ++i) {
        #pragma unroll
        for (int j = 0; j < REG_TILE_N; ++j) {
            int row = base_row + i;
            int col = base_col + j;
            if (row < M && col < N) {
                C[row * N + col] = reg_C[i][j];
            }
        }
    }
}
```

## 3. 双缓冲技术 (Double Buffering)

### 3.1 重叠内存访问和计算

```cpp
/**
 * @brief 双缓冲优化的矩阵乘法
 * 
 * 重叠下一轮数据加载与当前计算，提高GPU利用率
 */
__global__ void double_buffered_matmul(
    const double* __restrict__ A,
    const double* __restrict__ B,
    double* __restrict__ C,
    int M, int N, int K) {
    
    // 双缓冲共享内存
    __shared__ double tile_A[2][TILE_SIZE][TILE_SIZE + 1];
    __shared__ double tile_B[2][TILE_SIZE][TILE_SIZE + 1];
    
    int tx = threadIdx.x;
    int ty = threadIdx.y;
    int row = blockIdx.y * TILE_SIZE + ty;
    int col = blockIdx.x * TILE_SIZE + tx;
    
    double sum = 0.0;
    int write_stage = 0;
    int read_stage = 1;
    
    // 预加载第一个瓦片
    if (row < M && tx < K) {
        tile_A[write_stage][ty][tx] = A[row * K + tx];
    }
    if (col < N && ty < K) {
        tile_B[write_stage][ty][tx] = B[ty * N + col];
    }
    __syncthreads();
    
    for (int tile = 0; tile < (K + TILE_SIZE - 1) / TILE_SIZE - 1; ++tile) {
        // 交换缓冲区
        read_stage = write_stage;
        write_stage = 1 - write_stage;
        
        // 异步加载下一个瓦片
        int next_tile_start = (tile + 1) * TILE_SIZE;
        if (row < M && (next_tile_start + tx) < K) {
            tile_A[write_stage][ty][tx] = A[row * K + next_tile_start + tx];
        }
        if (col < N && (next_tile_start + ty) < K) {
            tile_B[write_stage][ty][tx] = B[(next_tile_start + ty) * N + col];
        }
        
        // 使用当前缓冲区进行计算
        #pragma unroll
        for (int k = 0; k < TILE_SIZE; ++k) {
            sum += tile_A[read_stage][ty][k] * tile_B[read_stage][k][tx];
        }
        
        __syncthreads();
    }
    
    // 处理最后一个瓦片
    #pragma unroll
    for (int k = 0; k < TILE_SIZE; ++k) {
        sum += tile_A[write_stage][ty][k] * tile_B[write_stage][k][tx];
    }
    
    if (row < M && col < N) {
        C[row * N + col] = sum;
    }
}
```

## 4. 内存访问优化

### 4.1 合并内存访问（Coalesced Memory Access）

```cpp
/**
 * @brief 优化内存访问模式
 * 
 * 确保warp内的线程访问连续内存地址
 */
__global__ void coalesced_matmul(
    const double* __restrict__ A,
    const double* __restrict__ B,
    double* __restrict__ C,
    int M, int N, int K) {
    
    __shared__ double tile_A[TILE_SIZE][TILE_SIZE + 1];
    __shared__ double tile_B[TILE_SIZE][TILE_SIZE + 1];
    
    int tx = threadIdx.x;
    int ty = threadIdx.y;
    int warp_id = threadIdx.y;
    int lane_id = threadIdx.x;
    
    // 确保合并访问：warp内线程访问连续地址
    for (int tile = 0; tile < (K + TILE_SIZE - 1) / TILE_SIZE; ++tile) {
        // 使用向量化加载优化
        if (sizeof(double) == 8) {
            // 128位向量化加载（2个double）
            double2* vec_A = (double2*)&A[(blockIdx.y * TILE_SIZE + ty) * K + tile * TILE_SIZE];
            double2* vec_tile_A = (double2*)&tile_A[ty][0];
            
            if (tx < TILE_SIZE / 2) {
                vec_tile_A[tx] = vec_A[tx];
            }
        }
        
        __syncthreads();
        
        // 计算逻辑...
        
        __syncthreads();
    }
}
```

### 4.2 预取优化

```cpp
/**
 * @brief 内存预取优化
 */
__device__ __forceinline__ void prefetch_data(const void* addr) {
    #if __CUDA_ARCH__ >= 200
    asm("prefetch.global.L2 [%0];" :: "l"(addr));
    #endif
}

__global__ void prefetch_optimized_matmul(
    const double* __restrict__ A,
    const double* __restrict__ B,
    double* __restrict__ C,
    int M, int N, int K) {
    
    // 预取下一轮需要的数据
    const double* next_A = A + (blockIdx.y * TILE_SIZE + threadIdx.y + TILE_SIZE) * K;
    const double* next_B = B + (threadIdx.y + TILE_SIZE) * N + blockIdx.x * TILE_SIZE + threadIdx.x;
    
    prefetch_data(next_A);
    prefetch_data(next_B);
    
    // 继续正常的矩阵乘法计算...
}
```

## 5. 数值精度优化

### 5.1 混合精度计算

```cpp
/**
 * @brief 混合精度矩阵乘法
 * 
 * 使用半精度存储，单精度计算，提高带宽利用率
 */
__global__ void mixed_precision_matmul(
    const __half* __restrict__ A,
    const __half* __restrict__ B,
    float* __restrict__ C,
    int M, int N, int K) {
    
    __shared__ __half tile_A[TILE_SIZE][TILE_SIZE + 1];
    __shared__ __half tile_B[TILE_SIZE][TILE_SIZE + 1];
    
    float sum = 0.0f;
    
    for (int tile = 0; tile < (K + TILE_SIZE - 1) / TILE_SIZE; ++tile) {
        // 加载半精度数据...
        
        __syncthreads();
        
        // 使用单精度进行计算
        #pragma unroll
        for (int k = 0; k < TILE_SIZE; ++k) {
            sum += __half2float(tile_A[threadIdx.y][k]) * 
                   __half2float(tile_B[k][threadIdx.x]);
        }
        
        __syncthreads();
    }
    
    // 存储为单精度结果
    if (threadIdx.y + blockIdx.y * TILE_SIZE < M && 
        threadIdx.x + blockIdx.x * TILE_SIZE < N) {
        C[(threadIdx.y + blockIdx.y * TILE_SIZE) * N + 
          (threadIdx.x + blockIdx.x * TILE_SIZE)] = sum;
    }
}
```

## 6. 算法级优化

### 6.1 Winograd算法（适用于小矩阵）

```cpp
/**
 * @brief Winograd算法优化的2x2矩阵乘法
 * 
 * 减少乘法运算次数，适用于神经网络卷积
 */
__device__ void winograd_2x2_matmul(
    const double* A, const double* B, double* C) {
    
    // Winograd算法：用7次乘法代替8次
    double m1 = (A[0] + A[3]) * (B[0] + B[3]);
    double m2 = (A[2] + A[3]) * B[0];
    double m3 = A[0] * (B[1] - B[3]);
    double m4 = A[3] * (B[2] - B[0]);
    double m5 = (A[0] + A[1]) * B[3];
    double m6 = (A[2] - A[0]) * (B[0] + B[1]);
    double m7 = (A[1] - A[3]) * (B[2] + B[3]);
    
    C[0] = m1 + m4 - m5 + m7;
    C[1] = m3 + m5;
    C[2] = m2 + m4;
    C[3] = m1 + m3 - m2 + m6;
}
```

## 7. 动态调优和自适应优化

### 7.1 运行时参数选择

```cpp
/**
 * @brief 自适应矩阵乘法调用器
 * 
 * 根据矩阵大小动态选择最优算法
 */
class AdaptiveMatmulDispatcher {
private:
    struct PerfProfile {
        int tile_size;
        int reg_tile_m, reg_tile_n;
        bool use_double_buffer;
        double gflops;
    };
    
    std::map<std::tuple<int,int,int>, PerfProfile> perf_cache;
    
public:
    void benchmark_and_cache(int M, int N, int K) {
        std::vector<PerfProfile> configs = {
            {16, 2, 2, false, 0.0},
            {32, 4, 4, false, 0.0},
            {32, 4, 4, true, 0.0},
            {64, 8, 8, true, 0.0}
        };
        
        double best_gflops = 0.0;
        PerfProfile best_config;
        
        for (auto& config : configs) {
            config.gflops = benchmark_config(M, N, K, config);
            if (config.gflops > best_gflops) {
                best_gflops = config.gflops;
                best_config = config;
            }
        }
        
        perf_cache[{M, N, K}] = best_config;
    }
    
    void dispatch_matmul(
        const double* A, const double* B, double* C,
        int M, int N, int K) {
        
        auto key = std::make_tuple(M, N, K);
        if (perf_cache.find(key) == perf_cache.end()) {
            benchmark_and_cache(M, N, K);
        }
        
        const auto& config = perf_cache[key];
        
        if (config.use_double_buffer) {
            launch_double_buffered_kernel(A, B, C, M, N, K, config);
        } else {
            launch_tiled_kernel(A, B, C, M, N, K, config);
        }
    }
    
private:
    double benchmark_config(int M, int N, int K, const PerfProfile& config);
    void launch_tiled_kernel(const double* A, const double* B, double* C,
                           int M, int N, int K, const PerfProfile& config);
    void launch_double_buffered_kernel(const double* A, const double* B, double* C,
                                     int M, int N, int K, const PerfProfile& config);
};
```

## 8. 针对您的MLP代码的具体优化建议

### 8.1 替换现有的matmul函数

```cpp
// 替换 main-3.cpp 中的 matmul 函数
__global__ void matmul(const double* A, const double* B, double* C, int M, int N, int K) {
    // 使用上述优化的 optimized_matmul_tiled 实现
    // 或根据具体矩阵大小选择最优实现
    
    #if TILE_SIZE == 32
        // 调用 32x32 瓦片的优化版本
        optimized_matmul_tiled(A, B, C, M, N, K);
    #elif TILE_SIZE == 16  
        // 调用 16x16 瓦片的优化版本
        // ...
    #endif
}
```

### 8.2 批处理优化

```cpp
/**
 * @brief 批量矩阵乘法优化
 * 
 * 同时处理多个小批量，提高GPU利用率
 */
__global__ void batched_matmul(
    const double* A, const double* B, double* C,
    int batch_size, int M, int N, int K) {
    
    int batch_id = blockIdx.z;
    if (batch_id >= batch_size) return;
    
    const double* A_batch = A + batch_id * M * K;
    const double* B_batch = B + batch_id * K * N;
    double* C_batch = C + batch_id * M * N;
    
    // 调用优化的单矩阵乘法
    optimized_matmul_tiled(A_batch, B_batch, C_batch, M, N, K);
}
```

## 9. 性能测量和分析

### 9.1 性能基准测试

```cpp
/**
 * @brief 性能测试工具
 */
class MatmulBenchmark {
public:
    static double measure_gflops(
        std::function<void()> kernel_func,
        int M, int N, int K,
        int iterations = 100) {
        
        hipEvent_t start, stop;
        hipEventCreate(&start);
        hipEventCreate(&stop);
        
        // 预热
        kernel_func();
        hipDeviceSynchronize();
        
        hipEventRecord(start);
        for (int i = 0; i < iterations; ++i) {
            kernel_func();
        }
        hipEventRecord(stop);
        hipEventSynchronize(stop);
        
        float elapsed_ms;
        hipEventElapsedTime(&elapsed_ms, start, stop);
        
        double total_ops = 2.0 * M * N * K * iterations;
        double gflops = total_ops / (elapsed_ms * 1e6);
        
        hipEventDestroy(start);
        hipEventDestroy(stop);
        
        return gflops;
    }
};
```

## 10. 实际应用建议

### 10.1 针对您的神经网络优化策略

1. **矩阵维度分析**：您的代码中包含小矩阵（10×32, 32×1），建议使用较小的瓦片大小（16×16）
2. **批处理优化**：BATCH_SIZE=256较大，可以充分利用GPU并行性
3. **内存管理**：考虑使用GPU内存池避免频繁分配释放
4. **混合精度**：可以考虑使用半精度存储，单精度计算来提高带宽

### 10.2 编译优化标志

```bash
# 优化的编译命令
hipcc main-3.cpp -o mlp_optimized \
    -O3 \
    -DNDEBUG \
    -mcumode \
    --offload-arch=gfx906 \  # 根据您的GPU架构调整
    -ffast-math \
    -funroll-loops
```

通过实施这些优化策略，您的MLP代码性能应该能够提升2-10倍，具体提升幅度取决于硬件配置和矩阵规模。建议逐步实施这些优化，每次添加一种技术并测量性能变化。 