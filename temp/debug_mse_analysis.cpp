// MSE分析调试代码
#include <iostream>
#include <vector>
#include <cmath>

void analyze_mse() {
    // 假设的归一化参数（需要从实际运行中获取）
    double min_val = 100.0;  // 假设最小值
    double max_val = 300.0;  // 假设最大值
    
    // 反归一化后的实际值对比
    std::vector<std::pair<double, double>> predictions = {
        {247.127, 259.88},
        {236.08, 256.84},
        {244.527, 252.1},
        {245.562, 246.65},
        {241.329, 258.93}
    };
    
    std::cout << "=== MSE分析 ===" << std::endl;
    std::cout << "假设归一化范围: [" << min_val << ", " << max_val << "]" << std::endl;
    std::cout << "归一化范围宽度: " << (max_val - min_val) << std::endl << std::endl;
    
    double total_mse_with_half = 0.0;
    double total_mse_standard = 0.0;
    
    for (size_t i = 0; i < predictions.size(); ++i) {
        double pred_orig = predictions[i].first;
        double actual_orig = predictions[i].second;
        
        // 归一化
        double pred_norm = (pred_orig - min_val) / (max_val - min_val);
        double actual_norm = (actual_orig - min_val) / (max_val - min_val);
        
        // 计算差值
        double diff_orig = pred_orig - actual_orig;
        double diff_norm = pred_norm - actual_norm;
        
        // MSE计算
        double mse_with_half = 0.5 * diff_norm * diff_norm;
        double mse_standard = diff_norm * diff_norm;
        
        total_mse_with_half += mse_with_half;
        total_mse_standard += mse_standard;
        
        std::cout << "样本 " << i+1 << ":" << std::endl;
        std::cout << "  原始差值: " << diff_orig << std::endl;
        std::cout << "  归一化差值: " << diff_norm << std::endl;
        std::cout << "  0.5*MSE: " << mse_with_half << std::endl;
        std::cout << "  标准MSE: " << mse_standard << std::endl << std::endl;
    }
    
    double avg_mse_with_half = total_mse_with_half / predictions.size();
    double avg_mse_standard = total_mse_standard / predictions.size();
    
    std::cout << "=== 平均MSE ===" << std::endl;
    std::cout << "使用0.5系数的MSE: " << avg_mse_with_half << std::endl;
    std::cout << "标准MSE: " << avg_mse_standard << std::endl;
    std::cout << "实际程序报告的MSE: 0.0052612" << std::endl;
}

int main() {
    analyze_mse();
    return 0;
} 