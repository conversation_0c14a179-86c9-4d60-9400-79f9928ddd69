/*
 * ===========================================
 * 文件名: main_cuda_radix2_precomp.cu
 * 描述: CUDA Radix-2 NTT实现 (预计算旋转因子)
 * 目标: 通过预计算旋转因子优化性能
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_radix2_precomp.cu -o ntt_cuda_radix2_precomp
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>
#include <iomanip>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

// fRead: 从输入文件读取多项式系数、次数和模数
void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

// fCheck: 检查计算结果与期望输出的一致性
void fCheck(int *ab, int n, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

// qpow: 计算快速幂 (a^b) % p
inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// ========== CUDA Kernels ==========

__global__ void bit_reverse_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

__global__ void ntt_radix2_precomp_kernel(int *data, const int* twiddles, int len, int p, int n, int offset) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    long long w = twiddles[offset + local_id];
    
    int u = data[base + local_id];
    long long v = (1LL * data[base + local_id + half_len] * w) % p;
    
    data[base + local_id] = (u + v) % p;
    data[base + local_id + half_len] = (u - v + p) % p;
}

__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

// ========== Host Functions ==========

void precompute_twiddles(std::vector<int>& twiddles, int n, int p, bool inverse) {
    twiddles.resize(n); // n-1 is enough, but n is safer
    int offset = 0;
    for (int len = 2; len <= n; len <<= 1) {
        long long wn = qpow(3, (p - 1) / len, p);
        if (inverse) wn = qpow(wn, p - 2, p);
        long long w = 1;
        for (int i = 0; i < len / 2; i++) {
            twiddles[offset + i] = w;
            w = (w * wn) % p;
        }
        offset += len / 2;
    }
}

void cuda_ntt_radix2_precomp(int *h_data, int n, bool inverse, int p, int *d_twiddles) {
    int *d_data, *d_rev;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));
    
    std::vector<int> rev(n);
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }
    
    for(int i = 0; i < n; i++) {
        rev[i] = 0;
        for(int j = 0; j < lg; j++) {
            if(i & (1 << j)) {
                rev[i] |= 1 << (lg - 1 - j);
            }
        }
    }
    
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));
    
    int threads = min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    int offset = 0;
    for(int len = 2; len <= n; len <<= 1) {
        int half_len = len >> 1;
        int total_butterflies = n / len * half_len;
        threads = min(1024, total_butterflies);
        blocks = (total_butterflies + threads - 1) / threads;
        
        ntt_radix2_precomp_kernel<<<blocks, threads>>>(d_data, d_twiddles, len, p, n, offset);
        CHECK_CUDA(cudaDeviceSynchronize());
        offset += half_len;
    }
    
    if(inverse) {
        int inv_n = qpow(n, p-2, p);
        threads = min(1024, n);
        blocks = (n + threads - 1) / threads;
        scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
}

void cuda_poly_multiply_precomp(int *a, int *b, int *result, int n, int p) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> h_twiddles_fwd, h_twiddles_inv;
    precompute_twiddles(h_twiddles_fwd, lim, p, false);
    precompute_twiddles(h_twiddles_inv, lim, p, true);

    int *d_twiddles_fwd, *d_twiddles_inv;
    CHECK_CUDA(cudaMalloc(&d_twiddles_fwd, lim * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles_inv, lim * sizeof(int)));
    CHECK_CUDA(cudaMemcpy(d_twiddles_fwd, h_twiddles_fwd.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles_inv, h_twiddles_inv.data(), lim * sizeof(int), cudaMemcpyHostToDevice));
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    cuda_ntt_radix2_precomp(A.data(), lim, false, p, d_twiddles_fwd);
    cuda_ntt_radix2_precomp(B.data(), lim, false, p, d_twiddles_fwd);
    
    for(int i = 0; i < lim; i++) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }
    
    cuda_ntt_radix2_precomp(A.data(), lim, true, p, d_twiddles_inv);
    
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }

    CHECK_CUDA(cudaFree(d_twiddles_fwd));
    CHECK_CUDA(cudaFree(d_twiddles_inv));
}

int main() {
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    printf("\nCUDA Radix-2 NTT (预计算旋转因子) 测试:\n");
    printf("================================================================\n");
    
    int a[300000], b[300000], ab[300000];
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_precomp(a, b, ab, n, p);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
} 