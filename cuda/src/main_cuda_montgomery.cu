/*
 * ===========================================
 * 文件名: main_cuda_montgomery.cu
 * 描述: CUDA Montgomery规约优化的NTT实现
 * 目标: 使用Montgomery规约优化模运算性能
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_montgomery.cu -o ntt_cuda_montgomery
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// 计算模逆元（扩展欧几里得算法）
__host__ __device__ unsigned int mod_inverse(unsigned int a, unsigned int m) {
    if (m == 1) return 0;
    
    int m0 = m;
    int x0 = 0, x1 = 1;
    
    while (a > 1) {
        int q = a / m;
        int t = m;
        m = a % m;
        a = t;
        t = x0;
        x0 = x1 - q * x0;
        x1 = t;
    }
    
    if (x1 < 0) x1 += m0;
    return x1;
}

// Montgomery规约结构体
struct MontgomeryParams {
    unsigned int mod;
    unsigned int mod_inv;  // -mod^(-1) mod 2^32
    unsigned int r2;       // R^2 mod mod, where R = 2^32
    
    __host__ __device__ MontgomeryParams(unsigned int m = 0) : mod(m) {
        if (m == 0) {
            mod_inv = 0;
            r2 = 0;
            return;
        }
        
        // 计算 -mod^(-1) mod 2^32
        mod_inv = mod_inverse(m, 1U << 31);
        mod_inv = (~mod_inv + 1);  // 取负数
        
        // 计算 R^2 mod m
        unsigned long long r = 1;
        for (int i = 0; i < 64; ++i) {
            r = (r << 1) % m;
        }
        r2 = r;
    }
    
    // Montgomery规约
    __device__ inline unsigned int reduce(unsigned long long x) const {
        unsigned int q = (unsigned int)x * mod_inv;
        unsigned long long m = (unsigned long long)q * mod;
        unsigned int y = (x - m) >> 32;
        return (x < m) ? (y + mod) : y;
    }
    
    // 转换到Montgomery域
    __device__ inline unsigned int to_montgomery(unsigned int x) const {
        return reduce((unsigned long long)x * r2);
    }
    
    // 从Montgomery域转换回普通域
    __device__ inline unsigned int from_montgomery(unsigned int x) const {
        return reduce((unsigned long long)x);
    }
    
    // Montgomery域中的乘法
    __device__ inline unsigned int mul(unsigned int a, unsigned int b) const {
        return reduce((unsigned long long)a * b);
    }
    
    // Montgomery域中的加法
    __device__ inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int sum = a + b;
        return (sum >= mod) ? (sum - mod) : sum;
    }
    
    // Montgomery域中的减法
    __device__ inline unsigned int sub(unsigned int a, unsigned int b) const {
        return (a >= b) ? (a - b) : (a + mod - b);
    }
};

// CUDA Kernel: 位反转置换
__global__ void bit_reverse_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

// CUDA Kernel: 转换到Montgomery域
__global__ void to_montgomery_kernel(int *data, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)mont.to_montgomery((unsigned int)data[idx]);
    }
}

// CUDA Kernel: 从Montgomery域转换回普通域
__global__ void from_montgomery_kernel(int *data, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)mont.from_montgomery((unsigned int)data[idx]);
    }
}

// CUDA Kernel: Montgomery优化的Radix-2 NTT蝶形运算
__global__ void ntt_montgomery_kernel(int *data, int len, int wn, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    // 计算旋转因子 w^local_id (在Montgomery域中)
    unsigned int w = mont.to_montgomery(1);
    unsigned int wn_mont = (unsigned int)wn;  // wn已经在Montgomery域中
    for(int i = 0; i < local_id; i++) {
        w = mont.mul(w, wn_mont);
    }
    
    unsigned int u = (unsigned int)data[base + local_id];
    unsigned int v = mont.mul((unsigned int)data[base + local_id + half_len], w);
    
    data[base + local_id] = (int)mont.add(u, v);
    data[base + local_id + half_len] = (int)mont.sub(u, v);
}

// CUDA Kernel: Montgomery优化的预计算版本
__global__ void ntt_montgomery_precomp_kernel(int *data, const int *twiddles, int len, 
                                             MontgomeryParams mont, int n, int offset) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    unsigned int w = (unsigned int)twiddles[offset + local_id];
    unsigned int u = (unsigned int)data[base + local_id];
    unsigned int v = mont.mul((unsigned int)data[base + local_id + half_len], w);
    
    data[base + local_id] = (int)mont.add(u, v);
    data[base + local_id + half_len] = (int)mont.sub(u, v);
}

// CUDA Kernel: Montgomery优化的数组缩放
__global__ void scale_montgomery_kernel(int *data, int inv_n, MontgomeryParams mont, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)mont.mul((unsigned int)data[idx], (unsigned int)inv_n);
    }
}

// 预计算旋转因子（Montgomery域）
void precompute_twiddles_montgomery(std::vector<int>& twiddles, int n, int p, bool inverse, 
                                   const MontgomeryParams& mont) {
    twiddles.resize(n);
    int offset = 0;
    for (int len = 2; len <= n; len <<= 1) {
        long long wn = qpow(3, (p - 1) / len, p);
        if (inverse) wn = qpow(wn, p - 2, p);
        
        // 转换到Montgomery域
        unsigned int wn_mont = mont.to_montgomery((unsigned int)wn);
        unsigned int w = mont.to_montgomery(1);
        
        for (int i = 0; i < len / 2; i++) {
            twiddles[offset + i] = (int)w;
            w = mont.mul(w, wn_mont);
        }
        offset += len / 2;
    }
}

// CUDA Montgomery NTT
void cuda_ntt_montgomery(int *h_data, int n, bool inverse, int p, bool use_precomp = true) {
    int *d_data, *d_rev, *d_twiddles = nullptr;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));
    
    MontgomeryParams mont((unsigned int)p);
    
    // 准备位反转表
    std::vector<int> rev(n);
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }
    
    for(int i = 0; i < n; i++) {
        rev[i] = 0;
        for(int j = 0; j < lg; j++) {
            if(i & (1 << j)) {
                rev[i] |= 1 << (lg - 1 - j);
            }
        }
    }
    
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));
    
    // 转换到Montgomery域
    int threads = min(1024, n);
    int blocks = (n + threads - 1) / threads;
    to_montgomery_kernel<<<blocks, threads>>>(d_data, mont, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // 预计算旋转因子（如果启用）
    std::vector<int> twiddles;
    if (use_precomp) {
        CHECK_CUDA(cudaMalloc(&d_twiddles, n * sizeof(int)));
        precompute_twiddles_montgomery(twiddles, n, p, inverse, mont);
        CHECK_CUDA(cudaMemcpy(d_twiddles, twiddles.data(), n * sizeof(int), cudaMemcpyHostToDevice));
    }
    
    // 位反转置换
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // NTT蝶形运算阶段
    if (use_precomp) {
        int offset = 0;
        for(int len = 2; len <= n; len <<= 1) {
            int half_len = len >> 1;
            int total_butterflies = n / len * half_len;
            threads = min(1024, total_butterflies);
            blocks = (total_butterflies + threads - 1) / threads;
            
            ntt_montgomery_precomp_kernel<<<blocks, threads>>>(d_data, d_twiddles, len, mont, n, offset);
            CHECK_CUDA(cudaDeviceSynchronize());
            offset += half_len;
        }
    } else {
        for(int len = 2; len <= n; len <<= 1) {
            int wn = qpow(3, (p-1)/len, p);
            if(inverse) wn = qpow(wn, p-2, p);
            
            // 转换到Montgomery域
            int wn_mont = (int)mont.to_montgomery((unsigned int)wn);
            
            int half_len = len >> 1;
            int total_butterflies = n / len * half_len;
            threads = min(1024, total_butterflies);
            blocks = (total_butterflies + threads - 1) / threads;
            
            ntt_montgomery_kernel<<<blocks, threads>>>(d_data, len, wn_mont, mont, n);
            CHECK_CUDA(cudaDeviceSynchronize());
        }
    }
    
    // 逆变换的最终缩放
    if(inverse) {
        int inv_n = qpow(n, p-2, p);
        int inv_n_mont = (int)mont.to_montgomery((unsigned int)inv_n);
        threads = min(1024, n);
        blocks = (n + threads - 1) / threads;
        scale_montgomery_kernel<<<blocks, threads>>>(d_data, inv_n_mont, mont, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    // 从Montgomery域转换回普通域
    from_montgomery_kernel<<<blocks, threads>>>(d_data, mont, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
    if (d_twiddles) CHECK_CUDA(cudaFree(d_twiddles));
}

// 多项式乘法使用Montgomery优化
void cuda_poly_multiply_montgomery(int *a, int *b, int *result, int n, int p, bool use_precomp = true) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    // 正变换
    cuda_ntt_montgomery(A.data(), lim, false, p, use_precomp);
    cuda_ntt_montgomery(B.data(), lim, false, p, use_precomp);
    
    // 点乘（在普通域中进行）
    for(int i = 0; i < lim; i++) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }
    
    // 逆变换
    cuda_ntt_montgomery(A.data(), lim, true, p, use_precomp);
    
    // 拷贝结果
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }
}

int main() {
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("使用GPU: %s\n", prop.name);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);
    
    int a[300000], b[300000], ab[300000];
    
    printf("\nCUDA Montgomery规约优化 NTT 实现测试:\n");
    printf("================================================================\n");
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        // 测试基础版本
        memset(ab, 0, sizeof(ab));
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_montgomery(a, b, ab, n, p, false);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_basic = std::chrono::duration<double, std::milli>(end - start).count();
        printf("Montgomery基础版本执行时间: %.3f ms\n", time_basic);
        
        // 测试预计算版本
        memset(ab, 0, sizeof(ab));
        start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_montgomery(a, b, ab, n, p, true);
        end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_precomp = std::chrono::duration<double, std::milli>(end - start).count();
        printf("Montgomery预计算版本执行时间: %.3f ms\n", time_precomp);
        printf("加速比: %.2fx\n", time_basic / time_precomp);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
}
