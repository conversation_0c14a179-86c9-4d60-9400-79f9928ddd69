/*
 * ===========================================
 * 文件名: main_cuda_crt.cu
 * 描述: CUDA CRT（中国剩余定理）优化的NTT实现
 * 目标: 使用多个小模数并行计算，最后用CRT合并结果
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_crt.cu -o ntt_cuda_crt
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

// CRT模数（选择几个大的质数）
const int CRT_NUMS = 3;
const unsigned int CRT_MODS[CRT_NUMS] = {
    998244353,   // 119 * 2^23 + 1
    1004535809,  // 479 * 2^21 + 1  
    1012924417   // 483 * 2^21 + 1
};

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

inline long long qpow(long long x, long long y, unsigned int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// CUDA Kernel: 位反转置换
__global__ void bit_reverse_kernel(unsigned int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        unsigned int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

// CUDA Kernel: Radix-2 NTT蝶形运算
__global__ void ntt_kernel(unsigned int *data, int len, unsigned int wn, unsigned int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    // 计算旋转因子
    unsigned long long w = 1;
    for(int i = 0; i < local_id; i++) {
        w = (w * wn) % p;
    }
    
    unsigned int u = data[base + local_id];
    unsigned long long v = ((unsigned long long)data[base + local_id + half_len] * w) % p;
    
    data[base + local_id] = (u + v) % p;
    data[base + local_id + half_len] = (u + p - v) % p;
}

// CUDA Kernel: 数组缩放
__global__ void scale_kernel(unsigned int *data, unsigned int inv_n, int n, unsigned int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = ((unsigned long long)data[idx] * inv_n) % p;
    }
}

// CUDA Kernel: 点乘
__global__ void pointwise_mul_kernel(unsigned int *a, const unsigned int *b, int n, unsigned int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        a[idx] = ((unsigned long long)a[idx] * b[idx]) % p;
    }
}

// 单个模数的CUDA NTT
void cuda_ntt_single_mod(unsigned int *h_data, int n, bool inverse, unsigned int p) {
    unsigned int *d_data, *d_rev;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(unsigned int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));
    
    // 准备位反转表
    std::vector<int> rev(n);
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }
    
    for(int i = 0; i < n; i++) {
        rev[i] = 0;
        for(int j = 0; j < lg; j++) {
            if(i & (1 << j)) {
                rev[i] |= 1 << (lg - 1 - j);
            }
        }
    }
    
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(unsigned int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));
    
    // 位反转置换
    int threads = min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // NTT蝶形运算阶段
    for(int len = 2; len <= n; len <<= 1) {
        unsigned int wn = qpow(3, (p-1)/len, p);
        if(inverse) wn = qpow(wn, p-2, p);
        
        int half_len = len >> 1;
        int total_butterflies = n / len * half_len;
        threads = min(1024, total_butterflies);
        blocks = (total_butterflies + threads - 1) / threads;
        
        ntt_kernel<<<blocks, threads>>>(d_data, len, wn, p, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    // 逆变换的最终缩放
    if(inverse) {
        unsigned int inv_n = qpow(n, p-2, p);
        threads = min(1024, n);
        blocks = (n + threads - 1) / threads;
        scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(unsigned int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
}

// CUDA Kernel: CRT合并
__global__ void crt_combine_kernel(unsigned long long *result, 
                                  const unsigned int *data0, const unsigned int *data1, const unsigned int *data2,
                                  unsigned long long m0, unsigned long long m1, unsigned long long m2,
                                  unsigned long long inv01, unsigned long long inv02, unsigned long long inv12,
                                  int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx >= n) return;
    
    // 使用CRT公式合并三个模数的结果
    unsigned long long x0 = data0[idx];
    unsigned long long x1 = data1[idx];
    unsigned long long x2 = data2[idx];
    
    // 第一步：合并前两个模数
    unsigned long long diff1 = (x1 + m1 - x0 % m1) % m1;
    unsigned long long temp = x0 + m0 * ((diff1 * inv01) % m1);
    
    // 第二步：合并第三个模数
    unsigned long long m01 = m0 * m1;
    unsigned long long diff2 = (x2 + m2 - temp % m2) % m2;
    result[idx] = temp + m01 * ((diff2 * inv02) % m2);
}

// 计算模逆元
unsigned long long mod_inverse(unsigned long long a, unsigned long long m) {
    return qpow(a, m-2, m);
}

// CRT多项式乘法
void cuda_poly_multiply_crt(int *a, int *b, int *result, int n, int p) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    // 为每个CRT模数准备数据
    std::vector<std::vector<unsigned int>> A_mods(CRT_NUMS, std::vector<unsigned int>(lim, 0));
    std::vector<std::vector<unsigned int>> B_mods(CRT_NUMS, std::vector<unsigned int>(lim, 0));
    
    // 初始化数据
    for(int mod_idx = 0; mod_idx < CRT_NUMS; mod_idx++) {
        for(int i = 0; i < n; i++) {
            A_mods[mod_idx][i] = ((long long)a[i] % CRT_MODS[mod_idx] + CRT_MODS[mod_idx]) % CRT_MODS[mod_idx];
            B_mods[mod_idx][i] = ((long long)b[i] % CRT_MODS[mod_idx] + CRT_MODS[mod_idx]) % CRT_MODS[mod_idx];
        }
    }
    
    // 并行处理每个模数
    for(int mod_idx = 0; mod_idx < CRT_NUMS; mod_idx++) {
        // 正变换
        cuda_ntt_single_mod(A_mods[mod_idx].data(), lim, false, CRT_MODS[mod_idx]);
        cuda_ntt_single_mod(B_mods[mod_idx].data(), lim, false, CRT_MODS[mod_idx]);
        
        // 点乘
        unsigned int *d_a, *d_b;
        CHECK_CUDA(cudaMalloc(&d_a, lim * sizeof(unsigned int)));
        CHECK_CUDA(cudaMalloc(&d_b, lim * sizeof(unsigned int)));
        
        CHECK_CUDA(cudaMemcpy(d_a, A_mods[mod_idx].data(), lim * sizeof(unsigned int), cudaMemcpyHostToDevice));
        CHECK_CUDA(cudaMemcpy(d_b, B_mods[mod_idx].data(), lim * sizeof(unsigned int), cudaMemcpyHostToDevice));
        
        int threads = min(1024, lim);
        int blocks = (lim + threads - 1) / threads;
        pointwise_mul_kernel<<<blocks, threads>>>(d_a, d_b, lim, CRT_MODS[mod_idx]);
        CHECK_CUDA(cudaDeviceSynchronize());
        
        CHECK_CUDA(cudaMemcpy(A_mods[mod_idx].data(), d_a, lim * sizeof(unsigned int), cudaMemcpyDeviceToHost));
        CHECK_CUDA(cudaFree(d_a));
        CHECK_CUDA(cudaFree(d_b));
        
        // 逆变换
        cuda_ntt_single_mod(A_mods[mod_idx].data(), lim, true, CRT_MODS[mod_idx]);
    }
    
    // CRT合并
    unsigned int *d_data0, *d_data1, *d_data2;
    unsigned long long *d_result;
    
    CHECK_CUDA(cudaMalloc(&d_data0, lim * sizeof(unsigned int)));
    CHECK_CUDA(cudaMalloc(&d_data1, lim * sizeof(unsigned int)));
    CHECK_CUDA(cudaMalloc(&d_data2, lim * sizeof(unsigned int)));
    CHECK_CUDA(cudaMalloc(&d_result, lim * sizeof(unsigned long long)));
    
    CHECK_CUDA(cudaMemcpy(d_data0, A_mods[0].data(), lim * sizeof(unsigned int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_data1, A_mods[1].data(), lim * sizeof(unsigned int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_data2, A_mods[2].data(), lim * sizeof(unsigned int), cudaMemcpyHostToDevice));
    
    // 预计算CRT参数
    unsigned long long m0 = CRT_MODS[0];
    unsigned long long m1 = CRT_MODS[1];
    unsigned long long m2 = CRT_MODS[2];
    unsigned long long inv01 = mod_inverse(m0, m1);
    unsigned long long inv02 = mod_inverse(m0 * m1, m2);
    unsigned long long inv12 = mod_inverse(m1, m2);
    
    int threads = min(1024, lim);
    int blocks = (lim + threads - 1) / threads;
    crt_combine_kernel<<<blocks, threads>>>(d_result, d_data0, d_data1, d_data2,
                                           m0, m1, m2, inv01, inv02, inv12, lim);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // 拷贝结果并转换到目标模数
    std::vector<unsigned long long> combined_result(lim);
    CHECK_CUDA(cudaMemcpy(combined_result.data(), d_result, lim * sizeof(unsigned long long), cudaMemcpyDeviceToHost));
    
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = combined_result[i] % p;
    }
    
    CHECK_CUDA(cudaFree(d_data0));
    CHECK_CUDA(cudaFree(d_data1));
    CHECK_CUDA(cudaFree(d_data2));
    CHECK_CUDA(cudaFree(d_result));
}

int main() {
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("使用GPU: %s\n", prop.name);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);
    
    printf("CRT模数: ");
    for(int i = 0; i < CRT_NUMS; i++) {
        printf("%u ", CRT_MODS[i]);
    }
    printf("\n");
    
    int a[300000], b[300000], ab[300000];
    
    printf("\nCUDA CRT优化 NTT 实现测试:\n");
    printf("================================================================\n");
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_crt(a, b, ab, n, p);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
}
