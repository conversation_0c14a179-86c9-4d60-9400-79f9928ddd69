/*
 * ===========================================
 * 文件名: main_cuda_barrett.cu
 * 描述: CUDA Barrett规约优化的NTT实现
 * 目标: 使用Barrett规约优化模运算性能
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_barrett.cu -o ntt_cuda_barrett
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// Barrett规约结构体
struct BarrettParams {
    unsigned int mod;
    unsigned long long inv;

    __host__ __device__ BarrettParams(unsigned int m = 0) : mod(m) {
        if (m == 0) {
            inv = 0;
        } else {
            inv = (1ULL << 32) / m;  // floor(2^32 / m)
        }
    }
    
    __device__ inline unsigned int reduce(unsigned long long x) const {
        if (mod == 0) return (unsigned int)x;
        // 简化版本：直接使用模运算确保正确性
        return (unsigned int)(x % mod);
    }
    
    __device__ inline unsigned int mul(unsigned int a, unsigned int b) const {
        return reduce((unsigned long long)a * b);
    }
    
    __device__ inline unsigned int add(unsigned int a, unsigned int b) const {
        unsigned int sum = a + b;
        return (sum >= mod) ? (sum - mod) : sum;
    }
    
    __device__ inline unsigned int sub(unsigned int a, unsigned int b) const {
        return (a >= b) ? (a - b) : (a + mod - b);
    }
};

// CUDA Kernel: 位反转置换
__global__ void bit_reverse_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

// CUDA Kernel: Barrett优化的Radix-2 NTT蝶形运算
__global__ void ntt_barrett_kernel(int *data, int len, int wn, BarrettParams barrett, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    // 计算旋转因子 w^local_id
    unsigned int w = 1;
    unsigned int wn_u = (unsigned int)wn;
    for(int i = 0; i < local_id; i++) {
        w = barrett.mul(w, wn_u);
    }
    
    unsigned int u = (unsigned int)data[base + local_id];
    unsigned int v = barrett.mul((unsigned int)data[base + local_id + half_len], w);
    
    data[base + local_id] = (int)barrett.add(u, v);
    data[base + local_id + half_len] = (int)barrett.sub(u, v);
}

// CUDA Kernel: Barrett优化的预计算版本
__global__ void ntt_barrett_precomp_kernel(int *data, const int *twiddles, int len, 
                                          BarrettParams barrett, int n, int offset) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    unsigned int w = (unsigned int)twiddles[offset + local_id];
    unsigned int u = (unsigned int)data[base + local_id];
    unsigned int v = barrett.mul((unsigned int)data[base + local_id + half_len], w);
    
    data[base + local_id] = (int)barrett.add(u, v);
    data[base + local_id + half_len] = (int)barrett.sub(u, v);
}

// CUDA Kernel: Barrett优化的数组缩放
__global__ void scale_barrett_kernel(int *data, int inv_n, BarrettParams barrett, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (int)barrett.mul((unsigned int)data[idx], (unsigned int)inv_n);
    }
}

// 预计算旋转因子
void precompute_twiddles(std::vector<int>& twiddles, int n, int p, bool inverse) {
    twiddles.resize(n);
    int offset = 0;
    for (int len = 2; len <= n; len <<= 1) {
        long long wn = qpow(3, (p - 1) / len, p);
        if (inverse) wn = qpow(wn, p - 2, p);
        long long w = 1;
        for (int i = 0; i < len / 2; i++) {
            twiddles[offset + i] = w;
            w = (w * wn) % p;
        }
        offset += len / 2;
    }
}

// CUDA Barrett NTT
void cuda_ntt_barrett(int *h_data, int n, bool inverse, int p, bool use_precomp = true) {
    int *d_data, *d_rev, *d_twiddles = nullptr;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));
    
    BarrettParams barrett;
    barrett.mod = (unsigned int)p;
    barrett.inv = (1ULL << 32) / p;  // 在主机端计算

    // 准备位反转表
    std::vector<int> rev(n);
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }
    
    for(int i = 0; i < n; i++) {
        rev[i] = 0;
        for(int j = 0; j < lg; j++) {
            if(i & (1 << j)) {
                rev[i] |= 1 << (lg - 1 - j);
            }
        }
    }
    
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));
    
    // 预计算旋转因子（如果启用）
    std::vector<int> twiddles;
    if (use_precomp) {
        CHECK_CUDA(cudaMalloc(&d_twiddles, n * sizeof(int)));
        precompute_twiddles(twiddles, n, p, inverse);
        CHECK_CUDA(cudaMemcpy(d_twiddles, twiddles.data(), n * sizeof(int), cudaMemcpyHostToDevice));
    }
    
    // 位反转置换
    int threads = min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // NTT蝶形运算阶段
    if (use_precomp) {
        int offset = 0;
        for(int len = 2; len <= n; len <<= 1) {
            int half_len = len >> 1;
            int total_butterflies = n / len * half_len;
            threads = min(1024, total_butterflies);
            blocks = (total_butterflies + threads - 1) / threads;
            
            ntt_barrett_precomp_kernel<<<blocks, threads>>>(d_data, d_twiddles, len, barrett, n, offset);
            CHECK_CUDA(cudaDeviceSynchronize());
            offset += half_len;
        }
    } else {
        for(int len = 2; len <= n; len <<= 1) {
            int wn = qpow(3, (p-1)/len, p);
            if(inverse) wn = qpow(wn, p-2, p);
            
            int half_len = len >> 1;
            int total_butterflies = n / len * half_len;
            threads = min(1024, total_butterflies);
            blocks = (total_butterflies + threads - 1) / threads;
            
            ntt_barrett_kernel<<<blocks, threads>>>(d_data, len, wn, barrett, n);
            CHECK_CUDA(cudaDeviceSynchronize());
        }
    }
    
    // 逆变换的最终缩放
    if(inverse) {
        int inv_n = qpow(n, p-2, p);
        threads = min(1024, n);
        blocks = (n + threads - 1) / threads;
        scale_barrett_kernel<<<blocks, threads>>>(d_data, inv_n, barrett, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
    if (d_twiddles) CHECK_CUDA(cudaFree(d_twiddles));
}

// 多项式乘法使用Barrett优化
void cuda_poly_multiply_barrett(int *a, int *b, int *result, int n, int p, bool use_precomp = true) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    // 正变换
    cuda_ntt_barrett(A.data(), lim, false, p, use_precomp);
    cuda_ntt_barrett(B.data(), lim, false, p, use_precomp);
    
    // 点乘
    for(int i = 0; i < lim; i++) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }
    
    // 逆变换
    cuda_ntt_barrett(A.data(), lim, true, p, use_precomp);
    
    // 拷贝结果
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }
}

int main() {
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("使用GPU: %s\n", prop.name);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);
    
    int a[300000], b[300000], ab[300000];
    
    printf("\nCUDA Barrett规约优化 NTT 实现测试:\n");
    printf("================================================================\n");
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        // 测试基础版本
        memset(ab, 0, sizeof(ab));
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_barrett(a, b, ab, n, p, false);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_basic = std::chrono::duration<double, std::milli>(end - start).count();
        printf("Barrett基础版本执行时间: %.3f ms\n", time_basic);
        
        // 测试预计算版本
        memset(ab, 0, sizeof(ab));
        start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_barrett(a, b, ab, n, p, true);
        end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_precomp = std::chrono::duration<double, std::milli>(end - start).count();
        printf("Barrett预计算版本执行时间: %.3f ms\n", time_precomp);
        printf("加速比: %.2fx\n", time_basic / time_precomp);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
}
