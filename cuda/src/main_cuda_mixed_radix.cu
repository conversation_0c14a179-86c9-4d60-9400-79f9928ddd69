/*
 * ===========================================
 * 文件名: main_cuda_mixed_radix.cu
 * 描述: CUDA 混合Radix NTT实现
 * 目标: 根据数据大小和GPU特性自动选择最优的Radix配置
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_mixed_radix.cu -o ntt_cuda_mixed_radix
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// GPU性能特征结构体
struct GPUProfile {
    int sm_count;
    int max_threads_per_block;
    int max_shared_memory;
    size_t global_memory;
    
    // 根据GPU特性选择最优配置
    int get_optimal_block_size(int problem_size) const {
        if (problem_size < 1024) return 128;
        if (problem_size < 8192) return 256;
        if (problem_size < 65536) return 512;
        return min(1024, max_threads_per_block);
    }
    
    bool should_use_radix4(int n) const {
        // 检查n是否为4的幂次
        int lg = 0;
        int temp = n;
        while(temp > 1) {
            lg++;
            temp >>= 1;
        }
        
        // 只有当log2(n)为偶数且n足够大时才使用radix-4
        return (lg % 2 == 0) && (n >= 256);
    }
    
    bool should_use_precompute(int n) const {
        // 当n足够大且有足够内存时使用预计算
        size_t twiddle_memory = n * sizeof(int);
        return (n >= 512) && (twiddle_memory < global_memory / 10);
    }
};

// CUDA Kernel: 位反转置换
__global__ void bit_reverse_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

// CUDA Kernel: Radix-2 NTT蝶形运算
__global__ void ntt_radix2_kernel(int *data, int len, int wn, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    long long w = 1;
    for(int i = 0; i < local_id; i++) {
        w = (w * wn) % p;
    }
    
    int u = data[base + local_id];
    long long v = (1LL * data[base + local_id + half_len] * w) % p;
    
    data[base + local_id] = (u + v) % p;
    data[base + local_id + half_len] = (u - v + p) % p;
}

// CUDA Kernel: Radix-4 NTT蝶形运算
__global__ void ntt_radix4_kernel(int *data, int len, int wn, int i_mod, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int quarter_len = len >> 2;
    int block_id = idx / quarter_len;
    int local_id = idx % quarter_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + 3 * quarter_len >= n) return;
    
    // 计算旋转因子
    long long w1 = 1;
    for(int i = 0; i < local_id; i++) {
        w1 = (w1 * wn) % p;
    }
    long long w2 = (w1 * w1) % p;
    long long w3 = (w2 * w1) % p;
    
    // 读取四个数据点
    int a = data[base + local_id];
    int b = data[base + local_id + quarter_len];
    int c = data[base + local_id + 2 * quarter_len];
    int d = data[base + local_id + 3 * quarter_len];
    
    // 应用旋转因子
    b = (1LL * b * w1) % p;
    c = (1LL * c * w2) % p;
    d = (1LL * d * w3) % p;
    
    // Radix-4蝶形运算
    int t0 = (a + c) % p;
    int t1 = (a - c + p) % p;
    int t2 = (b + d) % p;
    int t3 = (b - d + p) % p;
    
    t3 = (1LL * t3 * i_mod) % p;
    
    data[base + local_id] = (t0 + t2) % p;
    data[base + local_id + quarter_len] = (t1 + t3) % p;
    data[base + local_id + 2 * quarter_len] = (t0 - t2 + p) % p;
    data[base + local_id + 3 * quarter_len] = (t1 - t3 + p) % p;
}

// CUDA Kernel: 数组缩放
__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

// 智能混合Radix NTT
void cuda_ntt_mixed_radix(int *h_data, int n, bool inverse, int p, const GPUProfile& gpu) {
    int *d_data, *d_rev;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));
    
    // 准备位反转表
    std::vector<int> rev(n);
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }
    
    bool use_radix4 = gpu.should_use_radix4(n);
    
    if (use_radix4) {
        // 4进制位反转
        int pairs = lg >> 1;
        for(int i = 0; i < n; i++) {
            int rev_val = 0;
            int tmp = i;
            for(int j = 0; j < pairs; j++) {
                rev_val = (rev_val << 2) | (tmp & 3);
                tmp >>= 2;
            }
            rev[i] = rev_val;
        }
    } else {
        // 2进制位反转
        for(int i = 0; i < n; i++) {
            rev[i] = 0;
            for(int j = 0; j < lg; j++) {
                if(i & (1 << j)) {
                    rev[i] |= 1 << (lg - 1 - j);
                }
            }
        }
    }
    
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));
    
    // 位反转置换
    int block_size = gpu.get_optimal_block_size(n);
    int threads = min(block_size, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // 选择NTT算法
    if (use_radix4) {
        printf("使用 Radix-4 算法 (n=%d, 块大小=%d)\n", n, block_size);
        
        // Radix-4 NTT
        for(int len = 4; len <= n; len <<= 2) {
            int wn = qpow(3, (p-1)/len, p);
            if(inverse) wn = qpow(wn, p-2, p);
            
            int i_mod = qpow(3, (p-1)/4, p);
            if(inverse) i_mod = qpow(i_mod, p-2, p);
            
            int quarter_len = len >> 2;
            int total_butterflies = n / len * quarter_len;
            threads = min(block_size, total_butterflies);
            blocks = (total_butterflies + threads - 1) / threads;
            
            ntt_radix4_kernel<<<blocks, threads>>>(d_data, len, wn, i_mod, p, n);
            CHECK_CUDA(cudaDeviceSynchronize());
        }
    } else {
        printf("使用 Radix-2 算法 (n=%d, 块大小=%d)\n", n, block_size);
        
        // Radix-2 NTT
        for(int len = 2; len <= n; len <<= 1) {
            int wn = qpow(3, (p-1)/len, p);
            if(inverse) wn = qpow(wn, p-2, p);
            
            int half_len = len >> 1;
            int total_butterflies = n / len * half_len;
            threads = min(block_size, total_butterflies);
            blocks = (total_butterflies + threads - 1) / threads;
            
            ntt_radix2_kernel<<<blocks, threads>>>(d_data, len, wn, p, n);
            CHECK_CUDA(cudaDeviceSynchronize());
        }
    }
    
    // 逆变换的最终缩放
    if(inverse) {
        int inv_n = qpow(n, p-2, p);
        threads = min(block_size, n);
        blocks = (n + threads - 1) / threads;
        scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
}

// 多项式乘法使用混合Radix
void cuda_poly_multiply_mixed_radix(int *a, int *b, int *result, int n, int p, const GPUProfile& gpu) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    // 正变换
    cuda_ntt_mixed_radix(A.data(), lim, false, p, gpu);
    cuda_ntt_mixed_radix(B.data(), lim, false, p, gpu);
    
    // 点乘
    for(int i = 0; i < lim; i++) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }
    
    // 逆变换
    cuda_ntt_mixed_radix(A.data(), lim, true, p, gpu);
    
    // 拷贝结果
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }
}

int main() {
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    
    // 构建GPU性能特征
    GPUProfile gpu;
    gpu.sm_count = prop.multiProcessorCount;
    gpu.max_threads_per_block = prop.maxThreadsPerBlock;
    gpu.max_shared_memory = prop.sharedMemPerBlock;
    gpu.global_memory = prop.totalGlobalMem;
    
    printf("🎯 CUDA 混合Radix NTT 智能优化测试\n");
    printf("================================================================\n");
    printf("GPU: %s\n", prop.name);
    printf("SM数量: %d\n", gpu.sm_count);
    printf("最大线程数每块: %d\n", gpu.max_threads_per_block);
    printf("全局内存: %.1f GB\n", gpu.global_memory / (1024.0 * 1024.0 * 1024.0));
    printf("================================================================\n\n");
    
    int a[300000], b[300000], ab[300000];
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        int lim = 1;
        while(lim < 2 * n) lim <<= 1;
        
        printf("变换大小: %d\n", lim);
        printf("推荐配置: ");
        if (gpu.should_use_radix4(lim)) {
            printf("Radix-4");
        } else {
            printf("Radix-2");
        }
        printf(", 块大小=%d", gpu.get_optimal_block_size(lim));
        if (gpu.should_use_precompute(lim)) {
            printf(", 预计算=是");
        } else {
            printf(", 预计算=否");
        }
        printf("\n");
        
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_mixed_radix(a, b, ab, n, p, gpu);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
}
