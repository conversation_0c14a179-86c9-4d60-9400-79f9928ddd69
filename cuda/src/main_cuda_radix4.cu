/*
 * ===========================================
 * 文件名: main_cuda_radix4.cu
 * 描述: CUDA Radix-4 NTT实现
 * 目标: 通过4叉蝶形运算提高性能
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_radix4.cu -o ntt_cuda_radix4
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

// fRead: 从输入文件读取多项式系数、次数和模数
// 参数: a(int*)-系数数组A, b(int*)-系数数组B, n(int*)-次数, p(int*)-模数, id(int)-测试用例ID
void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

// fCheck: 检查计算结果与期望输出的一致性
// 参数: ab(int*)-计算结果, n(int)-原始次数, id(int)-测试用例ID
void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

// fWrite: 将多项式乘积写入输出文件
// 参数: ab(int*)-乘积结果, n(int)-原始次数, input_id(int)-测试用例ID
void fWrite(int *ab, int n, int input_id) {
    std::string str1 = "files/";
    std::string str2 = std::to_string(input_id);
    std::string strout = str1 + str2 + ".out";
    std::ofstream fout(strout);
    for (int i = 0; i < n * 2 - 1; i++) {
        fout << ab[i] << '\n';
    }
}

// qpow: 计算快速幂 (a^b) % p
// 参数: x(long long)-底数, y(long long)-指数, p(int)-模数
// 返回: (long long) 结果
inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// CUDA Kernel: 4进制位反转置换
// 参数: data(int*)-数据数组, rev(int*)-位反转表, n(int)-数组大小
__global__ void bit_reverse_4_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

// CUDA Kernel: Radix-4 NTT蝶形运算
// 参数: data(int*)-数据数组, len(int)-当前长度, wn(int)-主单位根, i_mod(int)-虚数单位, p(int)-模数, n(int)-总大小
__global__ void ntt_radix4_kernel(int *data, int len, int wn, int i_mod, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int quarter_len = len >> 2;
    int block_id = idx / quarter_len;
    int local_id = idx % quarter_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + 3 * quarter_len >= n) return;
    
    // 计算旋转因子 w^j, w^(2j), w^(3j)
    long long w1 = 1;
    for(int i = 0; i < local_id; i++) {
        w1 = (w1 * wn) % p;
    }
    long long w2 = (w1 * w1) % p;
    long long w3 = (w2 * w1) % p;
    
    // 读取四个数据点
    int a = data[base + local_id];
    int b = data[base + local_id + quarter_len];
    int c = data[base + local_id + 2 * quarter_len];
    int d = data[base + local_id + 3 * quarter_len];
    
    // 应用旋转因子
    b = (1LL * b * w1) % p;
    c = (1LL * c * w2) % p;
    d = (1LL * d * w3) % p;
    
    // Radix-4蝶形运算
    int t0 = (a + c) % p;
    int t1 = (a - c + p) % p;
    int t2 = (b + d) % p;
    int t3 = (b - d + p) % p;
    
    // 使用预计算的虚数单位
    t3 = (1LL * t3 * i_mod) % p;
    
    // 输出四个结果
    data[base + local_id] = (t0 + t2) % p;
    data[base + local_id + quarter_len] = (t1 + t3) % p;
    data[base + local_id + 2 * quarter_len] = (t0 - t2 + p) % p;
    data[base + local_id + 3 * quarter_len] = (t1 - t3 + p) % p;
}

// CUDA Kernel: 数组缩放
// 参数: data(int*)-数据数组, inv_n(int)-缩放因子, n(int)-数组大小, p(int)-模数
__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

// Host function: CUDA Radix-4 NTT
// 参数: h_data(int*)-主机数据, n(int)-数组大小, inverse(bool)-是否逆变换, p(int)-模数
void cuda_ntt_radix4(int *h_data, int n, bool inverse, int p) {
    // 检查n是否为4的幂次，如果不是则回退到radix-2
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }
    
    if(lg & 1) {
        // 奇数log，无法使用radix-4，需要回退到radix-2
        printf("警告: log2(n)为奇数，无法使用radix-4，请使用radix-2实现\n");
        return;
    }
    
    // 分配设备内存
    int *d_data, *d_rev;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));
    
    // 准备4进制位反转表
    std::vector<int> rev(n);
    int pairs = lg >> 1;  // 2-bit pairs
    
    for(int i = 0; i < n; i++) {
        int rev_val = 0;
        int tmp = i;
        for(int j = 0; j < pairs; j++) {
            rev_val = (rev_val << 2) | (tmp & 3);
            tmp >>= 2;
        }
        rev[i] = rev_val;
    }
    
    // 拷贝数据到设备
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));
    
    // 4进制位反转置换
    int threads = min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_4_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // NTT蝶形运算阶段 (radix-4)
    for(int len = 4; len <= n; len <<= 2) {
        int wn = qpow(3, (p-1)/len, p);
        if(inverse) wn = qpow(wn, p-2, p);
        
        // 计算虚数单位 i = sqrt(-1) mod p
        int i_mod = qpow(3, (p-1)/4, p);
        if(inverse) i_mod = qpow(i_mod, p-2, p);  // 逆变换时使用 -i
        
        int quarter_len = len >> 2;
        int total_butterflies = n / len * quarter_len;
        threads = min(1024, total_butterflies);
        blocks = (total_butterflies + threads - 1) / threads;
        
        ntt_radix4_kernel<<<blocks, threads>>>(d_data, len, wn, i_mod, p, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    // 逆变换的最终缩放
    if(inverse) {
        int inv_n = qpow(n, p-2, p);
        threads = min(1024, n);
        blocks = (n + threads - 1) / threads;
        scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    // 拷贝结果回主机
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    
    // 清理
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
}

// 多项式乘法使用CUDA Radix-4 NTT
// 参数: a(int*)-多项式A, b(int*)-多项式B, result(int*)-结果, n(int)-次数, p(int)-模数
void cuda_poly_multiply_radix4(int *a, int *b, int *result, int n, int p) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    // 正变换
    cuda_ntt_radix4(A.data(), lim, false, p);
    cuda_ntt_radix4(B.data(), lim, false, p);
    
    // 点乘
    for(int i = 0; i < lim; i++) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }
    
    // 逆变换
    cuda_ntt_radix4(A.data(), lim, true, p);
    
    // 拷贝结果
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }
}

// 主函数
int main() {
    // 检查CUDA设备
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("使用GPU: %s\n", prop.name);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);
    
    // 测试数组
    int a[300000], b[300000], ab[300000];
    
    printf("\nCUDA Radix-4 NTT 实现测试:\n");
    printf("================================================================\n");
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_radix4(a, b, ab, n, p);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
} 