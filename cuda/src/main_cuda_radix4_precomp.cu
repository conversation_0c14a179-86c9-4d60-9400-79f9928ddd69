#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

// fRead: 从输入文件读取多项式系数、次数和模数
void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

// fCheck: 检查计算结果与期望输出的一致性
void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

// fWrite: 将多项式乘积写入输出文件
void fWrite(int *ab, int n, int input_id) {
    std::string str1 = "files/";
    std::string str2 = std::to_string(input_id);
    std::string strout = str1 + str2 + ".out";
    std::ofstream fout(strout);
    for (int i = 0; i < n * 2 - 1; i++) {
        fout << ab[i] << '\n';
    }
}

// qpow: 计算快速幂 (a^b) % p
inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// CUDA Kernel: 4进制位反转置换
__global__ void bit_reverse_4_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

// CUDA Kernel: Radix-4 NTT蝶形运算
__global__ void ntt_radix4_kernel(int *data, int len, const int *twiddles, int i_mod, int p, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int quarter_len = len >> 2;
    int block_id = idx / quarter_len;
    int local_id = idx % quarter_len;

    if(block_id * len >= n) return;

    int base = block_id * len;
    if(base + local_id + 3 * quarter_len >= n) return;

    // 预先计算的旋转因子
    int w1 = twiddles[local_id];
    int w2 = twiddles[local_id + quarter_len];
    int w3 = twiddles[local_id + 2 * quarter_len];

    // 读取四个数据点
    int a = data[base + local_id];
    int b = data[base + local_id + quarter_len];
    int c = data[base + local_id + 2 * quarter_len];
    int d = data[base + local_id + 3 * quarter_len];

    // 应用旋转因子
    b = (1LL * b * w1) % p;
    c = (1LL * c * w2) % p;
    d = (1LL * d * w3) % p;

    // Radix-4蝶形运算
    int t0 = (a + c) % p;
    int t1 = (a - c + p) % p;
    int t2 = (b + d) % p;
    int t3 = (b - d + p) % p;

    // 使用预计算的虚数单位
    t3 = (1LL * t3 * i_mod) % p;

    // 输出四个结果
    data[base + local_id] = (t0 + t2) % p;
    data[base + local_id + quarter_len] = (t1 + t3) % p;
    data[base + local_id + 2 * quarter_len] = (t0 - t2 + p) % p;
    data[base + local_id + 3 * quarter_len] = (t1 - t3 + p) % p;
}

// CUDA Kernel: 数组缩放
__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

__global__ void pointwise_mult_kernel(int *a, int *b, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        a[idx] = (1LL * a[idx] * b[idx]) % p;
    }
}

void cuda_ntt_radix4(int *h_data, int n, bool inverse, int p);

void cuda_poly_multiply_radix4(int* a, int* b, int* ab, int n, int p) {
    int N = 1;
    while(N < (2 * n - 1)) N <<= 2;

    std::vector<int> h_a(N, 0), h_b(N, 0);
    for(int i = 0; i < n; i++) {
        h_a[i] = a[i];
        h_b[i] = b[i];
    }

    cuda_ntt_radix4(h_a.data(), N, false, p);
    cuda_ntt_radix4(h_b.data(), N, false, p);

    int *d_a, *d_b;
    CHECK_CUDA(cudaMalloc(&d_a, N * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_b, N * sizeof(int)));
    CHECK_CUDA(cudaMemcpy(d_a, h_a.data(), N * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_b, h_b.data(), N * sizeof(int), cudaMemcpyHostToDevice));

    int threads = std::min(1024, N);
    int blocks = (N + threads - 1) / threads;
    pointwise_mult_kernel<<<blocks, threads>>>(d_a, d_b, N, p);
    CHECK_CUDA(cudaDeviceSynchronize());

    CHECK_CUDA(cudaMemcpy(h_a.data(), d_a, N * sizeof(int), cudaMemcpyDeviceToHost));
    
    cuda_ntt_radix4(h_a.data(), N, true, p);

    for(int i = 0; i < 2 * n - 1; i++) {
        ab[i] = h_a[i];
    }

    CHECK_CUDA(cudaFree(d_a));
    CHECK_CUDA(cudaFree(d_b));
}

// Host function: CUDA Radix-4 NTT
void cuda_ntt_radix4(int *h_data, int n, bool inverse, int p) {
    // 分配设备内存
    int *d_data, *d_rev, *d_twiddles;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_twiddles, (3 * n / 4) * sizeof(int)));

    // 准备4进制位反转表
    std::vector<int> rev(n);
    int bits = 0;
    if (n > 1) {
        int temp_n = n - 1;
        while(temp_n > 0) {
            temp_n >>= 1;
            bits++;
        }
    }
    int pairs = bits / 2;

    for(int i = 0; i < n; i++) {
        rev[i] = 0;
        int tmp = i;
        for(int j = 0; j < pairs; j++) {
            rev[i] = (rev[i] << 2) | (tmp & 3);
            tmp >>= 2;
        }
    }

    // 拷贝数据到设备
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));

    // 4进制位反转置换
    int threads = std::min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_4_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    std::vector<int> h_twiddles(3 * n / 4);

    // 计算虚数单位 i = sqrt(-1) mod p
    int i_mod = qpow(3, (p-1)/4, p);
    if(inverse) i_mod = qpow(i_mod, p-2, p);

    // NTT蝶形运算阶段 (radix-4)
    for(int len = 4; len <= n; len <<= 2) {
        int quarter_len = len >> 2;
        long long wn = qpow(3, (p - 1) / len, p);
        if (inverse) wn = qpow(wn, p - 2, p);

        long long current_w = 1;
        for (int k = 0; k < quarter_len; ++k) {
            h_twiddles[k] = static_cast<int>(current_w);
            long long w2 = (current_w * current_w) % p;
            h_twiddles[k + quarter_len] = static_cast<int>(w2);
            long long w3 = (w2 * current_w) % p;
            h_twiddles[k + 2 * quarter_len] = static_cast<int>(w3);
            current_w = (current_w * wn) % p;
        }
        CHECK_CUDA(cudaMemcpy(d_twiddles, h_twiddles.data(), (3 * quarter_len) * sizeof(int), cudaMemcpyHostToDevice));

        int total_butterflies = n / len * quarter_len;
        threads = std::min(1024, total_butterflies);
        blocks = (total_butterflies + threads - 1) / threads;

        ntt_radix4_kernel<<<blocks, threads>>>(d_data, len, d_twiddles, i_mod, p, n);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    // 逆变换的最终缩放
    if(inverse) {
        int inv_n = qpow(n, p-2, p);
        threads = std::min(1024, n);
        blocks = (n + threads - 1) / threads;
        scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    // 拷贝结果回主机
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));

    // 清理
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
    CHECK_CUDA(cudaFree(d_twiddles));
}

// 主函数
int main() {
    // 检查CUDA设备
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }

    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("使用GPU: %s\n", prop.name);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);

    // 测试数组
    int a[300000], b[300000], ab[300000];

    printf("\nCUDA Radix-4 NTT 实现测试:\n");
    printf("================================================================\n");

    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));

        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);

        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_radix4(a, b, ab, n, p);
        auto end = std::chrono::high_resolution_clock::now();

        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);

        printf("----------------------------------------\n");
    }

    return 0;
}
