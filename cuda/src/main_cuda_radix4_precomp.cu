#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>
#include <iomanip>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

struct TwiddleFactors {
    int w1;
    int w2;
    int w3;
};

// fRead & fCheck & qpow (与之前实现相同)
void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            std::cout << "结果错误" << std::endl;
            return;
        }
    }
    std::cout << "结果正确" << std::endl;
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// ========== CUDA Kernels ==========

__global__ void bit_reverse_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

__global__ void ntt_radix4_precomp_kernel_optimized(int *data, const TwiddleFactors* twiddles, int len, int p, int n, int offset, bool inverse, long long i_mod) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    int quarter_len = len >> 2;
    int block_id = idx / quarter_len;
    int local_id = idx % quarter_len;

    if(block_id * len >= n) return;

    int base = block_id * len;
    
    int i0 = base + local_id;
    int i1 = i0 + quarter_len;
    int i2 = i1 + quarter_len;
    int i3 = i2 + quarter_len;

    if(i3 >= n) return;

    TwiddleFactors tf = twiddles[offset + local_id];
    long long w1 = tf.w1;
    long long w2 = tf.w2;
    long long w3 = tf.w3;

    long long x0 = data[i0];
    long long x1 = data[i1];
    long long x2 = data[i2];
    long long x3 = data[i3];

    long long t0 = (x0 + x2) % p;
    long long t1 = (x0 - x2 + p) % p;
    long long t2 = (x1 + x3) % p;
    long long t3 = (x1 - x3 + p) % p;
    
    // 合并计算以减少条件判断
    long long neg_j_t3 = (inverse) ? (p - (1LL * t3 * i_mod % p)) % p : (1LL * t3 * i_mod % p);
    
    data[i0] = (t0 + t2) % p;
    data[i2] = (1LL * ((t0 - t2 + p) % p) * w2) % p;
    data[i1] = (1LL * ((t1 + neg_j_t3) % p) * w1) % p;
    data[i3] = (1LL * ((t1 - neg_j_t3 + p) % p) * w3) % p;
}

// 归一化缩放内核定义
__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        // 缩放操作：每个元素乘上inv_n并取模
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

void precompute_twiddles_radix4(std::vector<TwiddleFactors>& twiddles, int n, int p, bool inverse) {
    int size = 0;
    for (int len = 4; len <= n; len <<= 2) {
        size += len >> 2;
    }
    twiddles.resize(size);
    int offset = 0;
    long long i_mod = qpow(3, (p-1)/4, p);
    if(inverse) i_mod = qpow(i_mod, p-2, p);

    for (int len = 4; len <= n; len <<= 2) {
        long long wn = qpow(3, (p - 1) / len, p);
        if (inverse) wn = qpow(wn, p - 2, p);
        long long w = 1;
        for (int i = 0; i < len / 4; i++) {
            long long w2 = (w * w) % p;
            long long w3 = (w2 * w) % p;
            twiddles[offset + i] = { (int)w, (int)w2, (int)w3 };
            w = (w * wn) % p;
        }
        offset += len / 4;
    }
}

void cuda_ntt_radix4_precomp_optimized(int *h_data, int n, bool inverse, int p, TwiddleFactors *d_twiddles) {
    int *d_data, *d_rev;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));

    std::vector<int> rev(n);
    int lg = 0;
    int temp = n;
    while (temp > 1) {
        lg++;
        temp >>= 1;
    }
    for (int i = 0; i < n; i++) {
        rev[i] = 0;
        for (int j = 0; j < lg; j++) {
            if (i & (1 << j)) {
                rev[i] |= 1 << (lg - 1 - j);
            }
        }
    }
    
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));

    int threads = min(1024, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());

    long long i_mod = qpow(3, (p - 1) / 4, p); 

    int offset = 0;
    for(int len = 4; len <= n; len <<= 2) {
        int quarter_len = len >> 2;
        int total_butterflies = n / len * quarter_len;
        threads = min(1024, total_butterflies);
        blocks = (total_butterflies + threads - 1) / threads;
        
        ntt_radix4_precomp_kernel_optimized<<<blocks, threads>>>(d_data, d_twiddles, len, p, n, offset, inverse, i_mod);
        CHECK_CUDA(cudaDeviceSynchronize());

        offset += quarter_len;
    }

    if (inverse) {
        int inv_n = qpow(n, p - 2, p);
        threads = min(1024, n);
        blocks = (n + threads - 1) / threads;
        scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
        CHECK_CUDA(cudaDeviceSynchronize());
    }

    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
}

void cuda_poly_multiply_radix4_precomp(int *a, int *b, int *result, int n, int p) {
    int lim = 1;
    int lg = 0;
    while(lim < 2 * n) {
        lim <<= 1;
        lg++;
    }
    if (lg % 2 != 0) { // Radix-4 needs power of 4
        lim <<= 1;
        lg++;
    }

    std::vector<TwiddleFactors> h_twiddles_fwd, h_twiddles_inv;
    precompute_twiddles_radix4(h_twiddles_fwd, lim, p, false);
    precompute_twiddles_radix4(h_twiddles_inv, lim, p, true);

    TwiddleFactors *d_twiddles_fwd, *d_twiddles_inv;
    CHECK_CUDA(cudaMalloc(&d_twiddles_fwd, h_twiddles_fwd.size() * sizeof(TwiddleFactors)));
    CHECK_CUDA(cudaMalloc(&d_twiddles_inv, h_twiddles_inv.size() * sizeof(TwiddleFactors)));
    CHECK_CUDA(cudaMemcpy(d_twiddles_fwd, h_twiddles_fwd.data(), h_twiddles_fwd.size() * sizeof(TwiddleFactors), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_twiddles_inv, h_twiddles_inv.data(), h_twiddles_inv.size() * sizeof(TwiddleFactors), cudaMemcpyHostToDevice));

    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }

    cuda_ntt_radix4_precomp_optimized(A.data(), lim, false, p, d_twiddles_fwd);
    cuda_ntt_radix4_precomp_optimized(B.data(), lim, false, p, d_twiddles_fwd);
    
    for(int i=0; i<lim; ++i) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }

    cuda_ntt_radix4_precomp_optimized(A.data(), lim, true, p, d_twiddles_inv);

    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }
    
    CHECK_CUDA(cudaFree(d_twiddles_fwd));
    CHECK_CUDA(cudaFree(d_twiddles_inv));
}

int main() {
    printf("\nCUDA Radix-4 NTT (预计算旋转因子) 测试:\n");
    printf("================================================================\n");
    
    int a[300000], b[300000], ab[300000];
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        memset(ab, 0, sizeof(ab));
        
        printf("测试 %d: n=%d, p=%d\n", test_id, n, p);
        
        auto start = std::chrono::high_resolution_clock::now();
        cuda_poly_multiply_radix4_precomp(a, b, ab, n, p);
        auto end = std::chrono::high_resolution_clock::now();
        
        fCheck(ab, n, test_id);
        double time_ms = std::chrono::duration<double, std::milli>(end - start).count();
        printf("执行时间: %.3f ms\n", time_ms);
        
        printf("----------------------------------------\n");
    }
    
    return 0;
}
