/*
 * ===========================================
 * 文件名: main_cuda_comprehensive_test.cu
 * 描述: CUDA NTT综合性能测试
 * 目标: 对比所有CUDA NTT实现的性能，包括不同超参数的影响
 * 编译: nvcc -O3 -arch=sm_86 main_cuda_comprehensive_test.cu -o ntt_cuda_comprehensive_test
 * ===========================================
 */

#include <cuda_runtime.h>
#include <cuda.h>
#include <device_launch_parameters.h>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <fstream>
#include <chrono>
#include <vector>
#include <string>
#include <algorithm>
#include <iomanip>

#define CHECK_CUDA(call) \
    do { \
        cudaError_t err = call; \
        if (err != cudaSuccess) { \
            printf("CUDA error at %s:%d - %s\n", __FILE__, __LINE__, cudaGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while(0)

void fRead(int *a, int *b, int *n, int *p, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".in";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        exit(EXIT_FAILURE);
    }
    fin >> *n >> *p;
    for(int i = 0; i < *n; i++) fin >> a[i];
    for(int i = 0; i < *n; i++) fin >> b[i];
}

void fCheck(int *ab, int n, int id) {
    std::string path = "../nttdata/" + std::to_string(id) + ".out";
    std::ifstream fin(path);
    if (!fin.is_open()) {
        std::cerr << "错误: 无法打开文件 " << path << std::endl;
        return;
    }
    bool correct = true;
    for(int i = 0; i < 2*n-1; i++) {
        int x;
        fin >> x;
        if(x != ab[i]) {
            correct = false;
            break;
        }
    }
    if (!correct) {
        std::cout << "❌ 结果错误";
    } else {
        std::cout << "✅ 结果正确";
    }
}

inline long long qpow(long long x, long long y, int p) {
    long long res = 1;
    x %= p;
    while(y) {
        if(y & 1) res = res * x % p;
        x = x * x % p;
        y >>= 1;
    }
    return res;
}

// 基础Radix-2 NTT实现（用于性能对比）
__global__ void ntt_radix2_kernel(int *data, int len, int wn, int p, int n, int block_size) {
    int idx = blockIdx.x * block_size + threadIdx.x;
    int half_len = len >> 1;
    int block_id = idx / half_len;
    int local_id = idx % half_len;
    
    if(block_id * len >= n) return;
    
    int base = block_id * len;
    if(base + local_id + half_len >= n) return;
    
    long long w = 1;
    for(int i = 0; i < local_id; i++) {
        w = (w * wn) % p;
    }
    
    int u = data[base + local_id];
    long long v = (1LL * data[base + local_id + half_len] * w) % p;
    
    data[base + local_id] = (u + v) % p;
    data[base + local_id + half_len] = (u - v + p) % p;
}

__global__ void bit_reverse_kernel(int *data, int *rev, int n) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n && idx < rev[idx]) {
        int tmp = data[idx];
        data[idx] = data[rev[idx]];
        data[rev[idx]] = tmp;
    }
}

__global__ void scale_kernel(int *data, int inv_n, int n, int p) {
    int idx = blockIdx.x * blockDim.x + threadIdx.x;
    if(idx < n) {
        data[idx] = (1LL * data[idx] * inv_n) % p;
    }
}

// 可配置超参数的NTT实现
void cuda_ntt_configurable(int *h_data, int n, bool inverse, int p, int block_size, int grid_size_factor) {
    int *d_data, *d_rev;
    CHECK_CUDA(cudaMalloc(&d_data, n * sizeof(int)));
    CHECK_CUDA(cudaMalloc(&d_rev, n * sizeof(int)));
    
    // 准备位反转表
    std::vector<int> rev(n);
    int lg = 0;
    int temp = n;
    while(temp > 1) {
        lg++;
        temp >>= 1;
    }
    
    for(int i = 0; i < n; i++) {
        rev[i] = 0;
        for(int j = 0; j < lg; j++) {
            if(i & (1 << j)) {
                rev[i] |= 1 << (lg - 1 - j);
            }
        }
    }
    
    CHECK_CUDA(cudaMemcpy(d_data, h_data, n * sizeof(int), cudaMemcpyHostToDevice));
    CHECK_CUDA(cudaMemcpy(d_rev, rev.data(), n * sizeof(int), cudaMemcpyHostToDevice));
    
    // 位反转置换
    int threads = min(block_size, n);
    int blocks = (n + threads - 1) / threads;
    bit_reverse_kernel<<<blocks, threads>>>(d_data, d_rev, n);
    CHECK_CUDA(cudaDeviceSynchronize());
    
    // NTT蝶形运算阶段
    for(int len = 2; len <= n; len <<= 1) {
        int wn = qpow(3, (p-1)/len, p);
        if(inverse) wn = qpow(wn, p-2, p);
        
        int half_len = len >> 1;
        int total_butterflies = n / len * half_len;
        threads = min(block_size, total_butterflies);
        blocks = min(grid_size_factor * 108, (total_butterflies + threads - 1) / threads); // 108是典型的SM数量
        
        ntt_radix2_kernel<<<blocks, threads>>>(d_data, len, wn, p, n, block_size);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    // 逆变换的最终缩放
    if(inverse) {
        int inv_n = qpow(n, p-2, p);
        threads = min(block_size, n);
        blocks = (n + threads - 1) / threads;
        scale_kernel<<<blocks, threads>>>(d_data, inv_n, n, p);
        CHECK_CUDA(cudaDeviceSynchronize());
    }
    
    CHECK_CUDA(cudaMemcpy(h_data, d_data, n * sizeof(int), cudaMemcpyDeviceToHost));
    CHECK_CUDA(cudaFree(d_data));
    CHECK_CUDA(cudaFree(d_rev));
}

// 多项式乘法（可配置超参数）
void cuda_poly_multiply_configurable(int *a, int *b, int *result, int n, int p, 
                                    int block_size, int grid_size_factor) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    cuda_ntt_configurable(A.data(), lim, false, p, block_size, grid_size_factor);
    cuda_ntt_configurable(B.data(), lim, false, p, block_size, grid_size_factor);
    
    for(int i = 0; i < lim; i++) {
        A[i] = (1LL * A[i] * B[i]) % p;
    }
    
    cuda_ntt_configurable(A.data(), lim, true, p, block_size, grid_size_factor);
    
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = A[i];
    }
}

// 性能测试结构体
struct PerformanceResult {
    std::string method_name;
    double time_ms;
    bool correct;
    int block_size;
    int grid_size_factor;
    double speedup;
};

// CPU基准实现（简单版本）
void cpu_ntt_baseline(int *a, int *b, int *result, int n, int p) {
    int lim = 1;
    while(lim < 2 * n) lim <<= 1;
    
    std::vector<int> A(lim, 0), B(lim, 0);
    for(int i = 0; i < n; i++) {
        A[i] = a[i];
        B[i] = b[i];
    }
    
    // 简单的O(n^2)卷积作为基准
    std::vector<long long> conv(2*n-1, 0);
    for(int i = 0; i < n; i++) {
        for(int j = 0; j < n; j++) {
            conv[i+j] = (conv[i+j] + 1LL * a[i] * b[j]) % p;
        }
    }
    
    for(int i = 0; i < 2*n-1; i++) {
        result[i] = conv[i];
    }
}

int main() {
    int device_count;
    CHECK_CUDA(cudaGetDeviceCount(&device_count));
    if(device_count == 0) {
        std::cerr << "未找到CUDA设备!" << std::endl;
        return -1;
    }
    
    cudaDeviceProp prop;
    CHECK_CUDA(cudaGetDeviceProperties(&prop, 0));
    printf("🚀 CUDA NTT 综合性能测试\n");
    printf("================================================================\n");
    printf("GPU: %s\n", prop.name);
    printf("SM数量: %d\n", prop.multiProcessorCount);
    printf("最大线程数每块: %d\n", prop.maxThreadsPerBlock);
    printf("================================================================\n\n");
    
    int a[300000], b[300000], ab[300000];
    std::vector<PerformanceResult> results;
    
    // 测试不同的超参数配置
    std::vector<int> block_sizes = {64, 128, 256, 512, 1024};
    std::vector<int> grid_factors = {1, 2, 4, 8};
    
    for(int test_id = 0; test_id <= 3; test_id++) {
        int n, p;
        fRead(a, b, &n, &p, test_id);
        
        printf("📊 测试用例 %d: n=%d, p=%d\n", test_id, n, p);
        printf("----------------------------------------------------------------\n");
        
        // CPU基准测试（仅对小规模数据）
        if (n <= 1000) {
            memset(ab, 0, sizeof(ab));
            auto start = std::chrono::high_resolution_clock::now();
            cpu_ntt_baseline(a, b, ab, n, p);
            auto end = std::chrono::high_resolution_clock::now();
            
            double cpu_time = std::chrono::duration<double, std::milli>(end - start).count();
            printf("CPU基准 (O(n²)): %8.3f ms ", cpu_time);
            fCheck(ab, n, test_id);
            printf("\n");
            
            // 测试不同CUDA配置
            for(int block_size : block_sizes) {
                for(int grid_factor : grid_factors) {
                    if (block_size > prop.maxThreadsPerBlock) continue;
                    
                    memset(ab, 0, sizeof(ab));
                    start = std::chrono::high_resolution_clock::now();
                    cuda_poly_multiply_configurable(a, b, ab, n, p, block_size, grid_factor);
                    end = std::chrono::high_resolution_clock::now();
                    
                    double gpu_time = std::chrono::duration<double, std::milli>(end - start).count();
                    double speedup = cpu_time / gpu_time;
                    
                    printf("CUDA B%d G%d: %8.3f ms (%.1fx) ", 
                           block_size, grid_factor, gpu_time, speedup);
                    fCheck(ab, n, test_id);
                    printf("\n");
                    
                    PerformanceResult result;
                    result.method_name = "CUDA_B" + std::to_string(block_size) + "_G" + std::to_string(grid_factor);
                    result.time_ms = gpu_time;
                    result.block_size = block_size;
                    result.grid_size_factor = grid_factor;
                    result.speedup = speedup;
                    results.push_back(result);
                }
            }
        } else {
            // 对于大规模数据，只测试CUDA
            printf("大规模数据，跳过CPU基准测试\n");
            
            double best_time = 1e9;
            for(int block_size : block_sizes) {
                for(int grid_factor : grid_factors) {
                    if (block_size > prop.maxThreadsPerBlock) continue;
                    
                    memset(ab, 0, sizeof(ab));
                    auto start = std::chrono::high_resolution_clock::now();
                    cuda_poly_multiply_configurable(a, b, ab, n, p, block_size, grid_factor);
                    auto end = std::chrono::high_resolution_clock::now();
                    
                    double gpu_time = std::chrono::duration<double, std::milli>(end - start).count();
                    best_time = std::min(best_time, gpu_time);
                    
                    printf("CUDA B%d G%d: %8.3f ms ", block_size, grid_factor, gpu_time);
                    fCheck(ab, n, test_id);
                    printf("\n");
                }
            }
            printf("最佳GPU时间: %.3f ms\n", best_time);
        }
        
        printf("\n");
    }
    
    // 输出性能总结
    printf("🏆 性能总结\n");
    printf("================================================================\n");
    if (!results.empty()) {
        std::sort(results.begin(), results.end(), 
                 [](const PerformanceResult& a, const PerformanceResult& b) {
                     return a.speedup > b.speedup;
                 });
        
        printf("最佳配置 (按加速比排序):\n");
        for(int i = 0; i < std::min(5, (int)results.size()); i++) {
            printf("%d. 块大小=%d, 网格因子=%d, 加速比=%.1fx\n", 
                   i+1, results[i].block_size, results[i].grid_size_factor, results[i].speedup);
        }
    }
    
    return 0;
}
