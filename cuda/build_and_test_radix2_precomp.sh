#!/bin/bash

# ===========================================
# CUDA NTT 构建和测试脚本 - Radix-2 Precomp
# ===========================================

set -e

echo "=============================================="
echo "编译和测试 Radix-2 (预计算旋转因子)"
echo "=============================================="

export PATH=/usr/local/cuda-12.1/bin:$PATH
export LD_LIBRARY_PATH=/usr/local/cuda-12.1/lib64:$LD_LIBRARY_PATH

NVCC_FLAGS="-O3 -arch=sm_86 -std=c++14 -Xptxas -O3"

mkdir -p bin
mkdir -p files

echo "编译中..."
if nvcc $NVCC_FLAGS src/main_cuda_radix2_precomp.cu -o bin/ntt_cuda_radix2_precomp; then
    echo "✓ 编译成功"
else
    echo "✗ 编译失败"
    exit 1
fi

echo ""
echo "运行测试..."
cd bin
if ./ntt_cuda_radix2_precomp; then
    echo "✓ 测试完成"
else
    echo "✗ 测试失败"
fi
cd ..

echo "==============================================" 