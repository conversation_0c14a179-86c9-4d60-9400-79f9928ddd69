#!/usr/bin/env python3
"""
===========================================
CUDA NTT 性能分析报告生成器
描述: 分析CUDA NTT实现的性能数据并生成报告
使用: python3 performance_analysis.py
===========================================
"""

import subprocess
import json
import time
import os
import sys
from typing import Dict, List, Tuple
import matplotlib.pyplot as plt
import numpy as np
from datetime import datetime

class CUDANTTBenchmark:
    def __init__(self):
        self.results = {}
        self.gpu_info = self.get_gpu_info()
        
    def get_gpu_info(self) -> Dict:
        """获取GPU信息"""
        try:
            result = subprocess.run(['nvidia-smi', '--query-gpu=name,memory.total,compute_cap', 
                                   '--format=csv,noheader,nounits'], 
                                  capture_output=True, text=True)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                gpu_data = lines[0].split(', ')
                return {
                    'name': gpu_data[0],
                    'memory_mb': int(gpu_data[1]),
                    'compute_capability': gpu_data[2]
                }
        except:
            pass
        return {'name': 'Unknown', 'memory_mb': 0, 'compute_capability': 'Unknown'}
    
    def run_single_benchmark(self, executable: str, description: str) -> Dict:
        """运行单个基准测试"""
        print(f"🔄 运行 {description}...")
        
        if not os.path.exists(f"bin/{executable}"):
            print(f"❌ 可执行文件不存在: bin/{executable}")
            return None
            
        try:
            start_time = time.time()
            result = subprocess.run([f"./bin/{executable}"], 
                                  capture_output=True, text=True, timeout=120)
            end_time = time.time()
            
            if result.returncode == 0:
                # 解析输出中的时间信息
                times = self.parse_timing_output(result.stdout)
                return {
                    'description': description,
                    'success': True,
                    'total_time': end_time - start_time,
                    'test_times': times,
                    'stdout': result.stdout,
                    'stderr': result.stderr
                }
            else:
                print(f"❌ {description} 执行失败")
                return {
                    'description': description,
                    'success': False,
                    'error': result.stderr
                }
        except subprocess.TimeoutExpired:
            print(f"⏰ {description} 执行超时")
            return {
                'description': description,
                'success': False,
                'error': 'Timeout'
            }
        except Exception as e:
            print(f"❌ {description} 执行异常: {e}")
            return {
                'description': description,
                'success': False,
                'error': str(e)
            }
    
    def parse_timing_output(self, output: str) -> List[float]:
        """从输出中解析时间信息"""
        times = []
        lines = output.split('\n')
        
        for line in lines:
            if '执行时间:' in line or 'execution time:' in line or 'time:' in line:
                # 提取时间数值
                parts = line.split()
                for i, part in enumerate(parts):
                    if 'ms' in part:
                        try:
                            time_val = float(part.replace('ms', '').replace(':', ''))
                            times.append(time_val)
                        except:
                            pass
                    elif i > 0 and parts[i-1] in ['时间:', 'time:', '执行时间:']:
                        try:
                            time_val = float(part)
                            times.append(time_val)
                        except:
                            pass
        
        return times
    
    def run_all_benchmarks(self) -> Dict:
        """运行所有基准测试"""
        benchmarks = [
            ('ntt_cuda_radix2_basic', 'Radix-2 基础实现'),
            ('ntt_cuda_radix4', 'Radix-4 基础实现'),
            ('ntt_cuda_radix2_precomp', 'Radix-2 预计算优化'),
            ('ntt_cuda_radix4_precomp', 'Radix-4 预计算优化'),
            ('ntt_cuda_dif_dit', 'DIF/DIT 实现'),
            ('ntt_cuda_barrett', 'Barrett 规约优化'),
            ('ntt_cuda_montgomery', 'Montgomery 规约优化'),
            ('ntt_cuda_crt', 'CRT 并行实现'),
            ('ntt_cuda_mixed_radix', '混合Radix智能选择'),
        ]
        
        results = {}
        
        print("🚀 开始CUDA NTT性能基准测试")
        print("=" * 60)
        
        for executable, description in benchmarks:
            result = self.run_single_benchmark(executable, description)
            if result:
                results[executable] = result
            time.sleep(1)  # 避免GPU过热
        
        return results
    
    def generate_performance_report(self, results: Dict) -> str:
        """生成性能报告"""
        report = []
        report.append("# CUDA NTT 性能分析报告")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # GPU信息
        report.append("## GPU信息")
        report.append(f"- GPU型号: {self.gpu_info['name']}")
        report.append(f"- 显存大小: {self.gpu_info['memory_mb']} MB")
        report.append(f"- 计算能力: {self.gpu_info['compute_capability']}")
        report.append("")
        
        # 测试结果汇总
        report.append("## 测试结果汇总")
        report.append("| 实现方法 | 状态 | 平均时间(ms) | 最佳时间(ms) | 加速比 |")
        report.append("|---------|------|-------------|-------------|--------|")
        
        baseline_time = None
        successful_results = []
        
        for name, result in results.items():
            if result['success'] and result['test_times']:
                avg_time = np.mean(result['test_times'])
                min_time = np.min(result['test_times'])
                
                if baseline_time is None:
                    baseline_time = avg_time
                    speedup = 1.0
                else:
                    speedup = baseline_time / avg_time
                
                successful_results.append({
                    'name': name,
                    'description': result['description'],
                    'avg_time': avg_time,
                    'min_time': min_time,
                    'speedup': speedup
                })
                
                report.append(f"| {result['description']} | ✅ | {avg_time:.3f} | {min_time:.3f} | {speedup:.2f}x |")
            else:
                report.append(f"| {result['description']} | ❌ | - | - | - |")
        
        report.append("")
        
        # 性能分析
        if successful_results:
            report.append("## 性能分析")
            
            # 找出最快的实现
            fastest = min(successful_results, key=lambda x: x['avg_time'])
            report.append(f"- **最快实现**: {fastest['description']} ({fastest['avg_time']:.3f} ms)")
            
            # 找出加速比最高的实现
            if len(successful_results) > 1:
                best_speedup = max(successful_results, key=lambda x: x['speedup'])
                report.append(f"- **最佳加速比**: {best_speedup['description']} ({best_speedup['speedup']:.2f}x)")
            
            # 性能建议
            report.append("")
            report.append("### 性能建议")
            
            radix4_results = [r for r in successful_results if 'Radix-4' in r['description']]
            radix2_results = [r for r in successful_results if 'Radix-2' in r['description']]
            
            if radix4_results and radix2_results:
                avg_radix4 = np.mean([r['avg_time'] for r in radix4_results])
                avg_radix2 = np.mean([r['avg_time'] for r in radix2_results])
                
                if avg_radix4 < avg_radix2:
                    report.append("- 对于当前测试数据，Radix-4实现表现更好")
                else:
                    report.append("- 对于当前测试数据，Radix-2实现表现更好")
            
            precomp_results = [r for r in successful_results if '预计算' in r['description']]
            basic_results = [r for r in successful_results if '基础' in r['description']]
            
            if precomp_results and basic_results:
                avg_precomp = np.mean([r['avg_time'] for r in precomp_results])
                avg_basic = np.mean([r['avg_time'] for r in basic_results])
                
                if avg_precomp < avg_basic:
                    improvement = (avg_basic - avg_precomp) / avg_basic * 100
                    report.append(f"- 预计算优化平均提升性能 {improvement:.1f}%")
        
        report.append("")
        report.append("## 详细测试数据")
        
        for name, result in results.items():
            if result['success']:
                report.append(f"### {result['description']}")
                if result['test_times']:
                    report.append(f"- 测试次数: {len(result['test_times'])}")
                    report.append(f"- 平均时间: {np.mean(result['test_times']):.3f} ms")
                    report.append(f"- 标准差: {np.std(result['test_times']):.3f} ms")
                    report.append(f"- 最小时间: {np.min(result['test_times']):.3f} ms")
                    report.append(f"- 最大时间: {np.max(result['test_times']):.3f} ms")
                report.append("")
        
        return "\n".join(report)
    
    def save_results(self, results: Dict, filename: str = "performance_results.json"):
        """保存结果到JSON文件"""
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'gpu_info': self.gpu_info,
                'results': results
            }, f, indent=2, ensure_ascii=False)
        print(f"📊 结果已保存到 {filename}")
    
    def create_performance_chart(self, results: Dict):
        """创建性能对比图表"""
        try:
            successful_results = []
            
            for name, result in results.items():
                if result['success'] and result['test_times']:
                    avg_time = np.mean(result['test_times'])
                    successful_results.append({
                        'name': result['description'],
                        'time': avg_time
                    })
            
            if len(successful_results) < 2:
                print("⚠️  成功的测试结果不足，跳过图表生成")
                return
            
            # 按时间排序
            successful_results.sort(key=lambda x: x['time'])
            
            names = [r['name'] for r in successful_results]
            times = [r['time'] for r in successful_results]
            
            plt.figure(figsize=(12, 8))
            bars = plt.bar(range(len(names)), times, color='skyblue', edgecolor='navy', alpha=0.7)
            
            plt.xlabel('实现方法')
            plt.ylabel('平均执行时间 (ms)')
            plt.title(f'CUDA NTT 性能对比\nGPU: {self.gpu_info["name"]}')
            plt.xticks(range(len(names)), names, rotation=45, ha='right')
            
            # 在柱状图上添加数值标签
            for bar, time in zip(bars, times):
                plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(times)*0.01,
                        f'{time:.2f}', ha='center', va='bottom')
            
            plt.tight_layout()
            plt.savefig('cuda_ntt_performance.png', dpi=300, bbox_inches='tight')
            plt.close()
            
            print("📈 性能图表已保存到 cuda_ntt_performance.png")
            
        except ImportError:
            print("⚠️  matplotlib未安装，跳过图表生成")
        except Exception as e:
            print(f"❌ 图表生成失败: {e}")

def main():
    """主函数"""
    print("🔬 CUDA NTT 性能分析工具")
    print("=" * 50)
    
    # 检查是否在正确的目录
    if not os.path.exists('bin'):
        print("❌ 未找到bin目录，请先运行构建脚本")
        sys.exit(1)
    
    benchmark = CUDANTTBenchmark()
    
    # 运行基准测试
    results = benchmark.run_all_benchmarks()
    
    if not results:
        print("❌ 没有成功的测试结果")
        sys.exit(1)
    
    # 生成报告
    report = benchmark.generate_performance_report(results)
    
    # 保存报告
    with open('performance_report.md', 'w', encoding='utf-8') as f:
        f.write(report)
    print("📝 性能报告已保存到 performance_report.md")
    
    # 保存原始数据
    benchmark.save_results(results)
    
    # 生成图表
    benchmark.create_performance_chart(results)
    
    print("\n" + "=" * 50)
    print("✅ 性能分析完成！")
    print("📁 生成的文件:")
    print("   - performance_report.md (性能报告)")
    print("   - performance_results.json (原始数据)")
    print("   - cuda_ntt_performance.png (性能图表)")

if __name__ == "__main__":
    main()
