#!/bin/bash

# ===========================================
# CUDA NTT 全面构建和测试脚本
# 描述: 编译并测试所有CUDA NTT实现
# 使用: ./build_and_test_all.sh
# ===========================================

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_header() {
    echo -e "${CYAN}================================================================${NC}"
    echo -e "${CYAN}$1${NC}"
    echo -e "${CYAN}================================================================${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 设置CUDA环境
setup_cuda_env() {
    export PATH=/usr/local/cuda-12.1/bin:$PATH
    export LD_LIBRARY_PATH=/usr/local/cuda-12.1/lib64:$LD_LIBRARY_PATH
}

# 检查CUDA环境
check_cuda() {
    print_header "检查CUDA环境"

    # 设置CUDA环境变量
    setup_cuda_env

    if ! command -v nvcc &> /dev/null; then
        print_error "nvcc未找到，请确保CUDA已正确安装"
        print_info "尝试设置CUDA环境变量..."
        exit 1
    fi
    
    nvcc_version=$(nvcc --version | grep "release" | awk '{print $6}' | cut -c2-)
    print_success "CUDA版本: $nvcc_version"
    
    if ! nvidia-smi &> /dev/null; then
        print_warning "nvidia-smi未找到，可能无法检测GPU"
    else
        gpu_info=$(nvidia-smi --query-gpu=name --format=csv,noheader,nounits | head -1)
        print_success "检测到GPU: $gpu_info"
    fi
    echo
}

# 创建必要的目录
setup_directories() {
    print_header "设置目录结构"
    
    mkdir -p bin
    mkdir -p files
    
    print_success "目录创建完成"
    echo
}

# 编译单个CUDA程序
compile_cuda() {
    local source_file=$1
    local output_name=$2
    local arch=${3:-sm_75}  # 默认架构
    
    print_info "编译 $source_file -> $output_name"
    
    if nvcc -O3 -arch=$arch "src/$source_file" -o "bin/$output_name" 2>/dev/null; then
        print_success "编译成功: $output_name"
        return 0
    else
        print_error "编译失败: $source_file"
        return 1
    fi
}

# 运行单个测试
run_test() {
    local executable=$1
    local description=$2
    
    print_info "运行测试: $description"
    
    if [ -f "bin/$executable" ]; then
        echo -e "${PURPLE}--- $description 测试结果 ---${NC}"
        if timeout 60 "./bin/$executable"; then
            print_success "$description 测试完成"
        else
            print_error "$description 测试失败或超时"
        fi
        echo
    else
        print_error "可执行文件不存在: bin/$executable"
    fi
}

# 主编译函数
compile_all() {
    print_header "编译所有CUDA NTT实现"
    
    # 检测GPU架构
    if nvidia-smi &> /dev/null; then
        gpu_arch=$(nvidia-smi --query-gpu=compute_cap --format=csv,noheader,nounits 2>/dev/null | head -1 | tr -d '. ')
        if [ ! -z "$gpu_arch" ] && [ "$gpu_arch" != "" ]; then
            arch="sm_$gpu_arch"
            print_info "检测到GPU架构: $arch"
        else
            arch="sm_86"  # RTX 3090 默认架构
            print_warning "无法检测GPU架构，使用默认: $arch"
        fi
    else
        arch="sm_86"
        print_warning "无法检测GPU，使用默认架构: $arch"
    fi
    
    # 编译所有实现
    local success_count=0
    local total_count=0
    
    # 基础实现
    ((total_count++))
    if compile_cuda "main_cuda_radix2_basic.cu" "ntt_cuda_radix2_basic" $arch; then
        ((success_count++))
    fi
    
    ((total_count++))
    if compile_cuda "main_cuda_radix4.cu" "ntt_cuda_radix4" $arch; then
        ((success_count++))
    fi
    
    # 预计算优化版本
    ((total_count++))
    if compile_cuda "main_cuda_radix2_precomp.cu" "ntt_cuda_radix2_precomp" $arch; then
        ((success_count++))
    fi
    
    ((total_count++))
    if compile_cuda "main_cuda_radix4_precomp.cu" "ntt_cuda_radix4_precomp" $arch; then
        ((success_count++))
    fi
    
    # DIF/DIT实现
    ((total_count++))
    if compile_cuda "main_cuda_dif_dit.cu" "ntt_cuda_dif_dit" $arch; then
        ((success_count++))
    fi
    
    # Barrett规约优化
    ((total_count++))
    if compile_cuda "main_cuda_barrett.cu" "ntt_cuda_barrett" $arch; then
        ((success_count++))
    fi
    
    # Montgomery规约优化
    ((total_count++))
    if compile_cuda "main_cuda_montgomery.cu" "ntt_cuda_montgomery" $arch; then
        ((success_count++))
    fi
    
    # CRT实现
    ((total_count++))
    if compile_cuda "main_cuda_crt.cu" "ntt_cuda_crt" $arch; then
        ((success_count++))
    fi
    
    # 混合Radix实现
    ((total_count++))
    if compile_cuda "main_cuda_mixed_radix.cu" "ntt_cuda_mixed_radix" $arch; then
        ((success_count++))
    fi
    
    # 综合性能测试
    ((total_count++))
    if compile_cuda "main_cuda_comprehensive_test.cu" "ntt_cuda_comprehensive_test" $arch; then
        ((success_count++))
    fi
    
    echo
    print_success "编译完成: $success_count/$total_count 成功"
    
    if [ $success_count -eq $total_count ]; then
        return 0
    else
        return 1
    fi
}

# 运行所有测试
run_all_tests() {
    print_header "运行所有测试"
    
    # 检查测试数据
    if [ ! -d "../nttdata" ]; then
        print_error "测试数据目录不存在: ../nttdata"
        print_info "请确保nttdata目录在正确位置"
        return 1
    fi
    
    # 基础功能测试
    run_test "ntt_cuda_radix2_basic" "Radix-2 基础实现"
    run_test "ntt_cuda_radix4" "Radix-4 基础实现"
    run_test "ntt_cuda_radix2_precomp" "Radix-2 预计算优化"
    run_test "ntt_cuda_radix4_precomp" "Radix-4 预计算优化"
    
    # 高级优化测试
    run_test "ntt_cuda_dif_dit" "DIF/DIT 实现"
    run_test "ntt_cuda_barrett" "Barrett 规约优化"
    run_test "ntt_cuda_montgomery" "Montgomery 规约优化"
    run_test "ntt_cuda_crt" "CRT 并行实现"
    
    # 智能优化测试
    run_test "ntt_cuda_mixed_radix" "混合Radix智能选择"
    
    # 综合性能测试
    run_test "ntt_cuda_comprehensive_test" "综合性能对比"
}

# 性能基准测试
run_benchmark() {
    print_header "性能基准测试"
    
    if [ -f "bin/ntt_cuda_comprehensive_test" ]; then
        print_info "运行详细性能分析..."
        echo -e "${PURPLE}--- 综合性能基准测试 ---${NC}"
        timeout 300 "./bin/ntt_cuda_comprehensive_test"
        echo
    else
        print_warning "综合性能测试程序未编译成功，跳过基准测试"
    fi
}

# 清理函数
cleanup() {
    print_header "清理编译文件"
    
    if [ -d "bin" ]; then
        rm -rf bin/*
        print_success "清理bin目录"
    fi
    
    if [ -d "files" ]; then
        rm -rf files/*
        print_success "清理files目录"
    fi
}

# 显示帮助信息
show_help() {
    echo "CUDA NTT 构建和测试脚本"
    echo
    echo "用法: $0 [选项]"
    echo
    echo "选项:"
    echo "  -h, --help      显示此帮助信息"
    echo "  -c, --compile   仅编译，不运行测试"
    echo "  -t, --test      仅运行测试（假设已编译）"
    echo "  -b, --benchmark 运行性能基准测试"
    echo "  --clean         清理编译文件"
    echo "  --check         仅检查环境"
    echo
    echo "默认行为: 检查环境 -> 编译 -> 运行测试 -> 性能基准"
}

# 主函数
main() {
    case "${1:-}" in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--compile)
            check_cuda
            setup_directories
            compile_all
            ;;
        -t|--test)
            run_all_tests
            ;;
        -b|--benchmark)
            run_benchmark
            ;;
        --clean)
            cleanup
            ;;
        --check)
            check_cuda
            ;;
        "")
            # 默认完整流程
            check_cuda
            setup_directories
            if compile_all; then
                run_all_tests
                run_benchmark
            else
                print_error "编译失败，跳过测试"
                exit 1
            fi
            ;;
        *)
            print_error "未知选项: $1"
            show_help
            exit 1
            ;;
    esac
}

# 脚本入口
print_header "🚀 CUDA NTT 全面构建和测试系统"
main "$@"
print_success "脚本执行完成！"
