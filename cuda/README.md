# CUDA NTT 并行优化实现

本目录包含了Number Theoretic Transform (NTT)算法的全面CUDA并行优化实现，涵盖了多种算法变体和优化技术。

## 📁 目录结构

```
cuda/
├── src/                          # 源代码文件
│   ├── main_cuda_radix2_basic.cu      # Radix-2 基础实现
│   ├── main_cuda_radix4.cu            # Radix-4 基础实现
│   ├── main_cuda_radix2_precomp.cu    # Radix-2 预计算优化
│   ├── main_cuda_radix4_precomp.cu    # Radix-4 预计算优化
│   ├── main_cuda_dif_dit.cu           # DIF/DIT 实现
│   ├── main_cuda_barrett.cu           # Barrett 规约优化
│   ├── main_cuda_montgomery.cu        # Montgomery 规约优化
│   ├── main_cuda_crt.cu               # CRT 并行实现
│   ├── main_cuda_mixed_radix.cu       # 混合Radix智能选择
│   └── main_cuda_comprehensive_test.cu # 综合性能测试
├── bin/                          # 编译后的可执行文件
├── files/                        # 输出文件
├── build_and_test_all.sh         # 自动构建和测试脚本
├── performance_analysis.py       # 性能分析工具
└── README.md                     # 本文档
```

## 🚀 快速开始

### 环境要求

- CUDA Toolkit (推荐 11.0+)
- 支持CUDA的NVIDIA GPU
- GCC/G++ 编译器
- Python 3.6+ (用于性能分析，可选)

### 一键构建和测试

```bash
# 给脚本执行权限
chmod +x build_and_test_all.sh

# 运行完整的构建和测试流程
./build_and_test_all.sh

# 或者分步执行
./build_and_test_all.sh --check      # 仅检查环境
./build_and_test_all.sh --compile    # 仅编译
./build_and_test_all.sh --test       # 仅运行测试
./build_and_test_all.sh --benchmark  # 运行性能基准测试
```

### 手动编译单个实现

```bash
# 编译Radix-2基础实现
nvcc -O3 -arch=sm_75 src/main_cuda_radix2_basic.cu -o bin/ntt_cuda_radix2_basic

# 编译Radix-4实现
nvcc -O3 -arch=sm_75 src/main_cuda_radix4.cu -o bin/ntt_cuda_radix4

# 编译Barrett规约优化
nvcc -O3 -arch=sm_75 src/main_cuda_barrett.cu -o bin/ntt_cuda_barrett
```

## 🔬 实现详解

### 1. 基础实现

#### Radix-2 NTT (`main_cuda_radix2_basic.cu`)
- **特点**: 经典的二进制NTT算法
- **优势**: 实现简单，适用于所有2的幂次大小
- **适用场景**: 小到中等规模数据，学习和验证

#### Radix-4 NTT (`main_cuda_radix4.cu`)
- **特点**: 四进制蝶形运算，减少循环层数
- **优势**: 理论上比Radix-2快2倍
- **限制**: 仅适用于log₂(n)为偶数的情况
- **适用场景**: 大规模数据，追求最高性能

### 2. 预计算优化

#### 预计算旋转因子 (`*_precomp.cu`)
- **原理**: 将旋转因子预先计算并存储在GPU内存中
- **优势**: 避免重复计算，减少计算延迟
- **内存开销**: 额外的O(n)内存使用
- **性能提升**: 通常有10-30%的性能改善

### 3. 算法变体

#### DIF/DIT 实现 (`main_cuda_dif_dit.cu`)
- **DIF (Decimation-In-Frequency)**: 频域抽取算法
- **DIT (Decimation-In-Time)**: 时域抽取算法
- **特点**: 不同的数据访问模式，可能在某些GPU上表现更好

### 4. 模运算优化

#### Barrett 规约 (`main_cuda_barrett.cu`)
- **原理**: 使用预计算的倒数避免除法运算
- **优势**: 在模数固定时显著提升性能
- **适用**: 高频模运算场景

#### Montgomery 规约 (`main_cuda_montgomery.cu`)
- **原理**: 将数值转换到Montgomery域进行运算
- **优势**: 避免昂贵的模运算
- **复杂度**: 需要域转换开销

### 5. 并行策略

#### CRT 实现 (`main_cuda_crt.cu`)
- **原理**: 使用中国剩余定理，多个小模数并行计算
- **优势**: 可以处理超大模数，提高并行度
- **应用**: 大整数运算，密码学应用

#### 混合Radix (`main_cuda_mixed_radix.cu`)
- **特点**: 根据数据大小和GPU特性自动选择最优算法
- **智能**: 动态调整块大小、网格大小等参数
- **目标**: 在不同场景下都能获得最佳性能

## 📊 性能分析

### 自动性能分析

```bash
# 运行性能分析工具
python3 performance_analysis.py
```

生成的报告包括：
- `performance_report.md`: 详细的性能分析报告
- `performance_results.json`: 原始测试数据
- `cuda_ntt_performance.png`: 性能对比图表

### 手动性能测试

```bash
# 运行综合性能测试
./bin/ntt_cuda_comprehensive_test

# 测试特定实现
./bin/ntt_cuda_radix4_precomp
./bin/ntt_cuda_barrett
```

## 🎯 优化策略

### 1. 内存优化
- **合并内存访问**: 确保线程访问连续内存
- **预计算存储**: 将频繁使用的数据预存在GPU内存
- **共享内存利用**: 在适当场景下使用共享内存

### 2. 计算优化
- **减少分支**: 最小化条件分支以避免线程发散
- **向量化操作**: 利用GPU的SIMD特性
- **流水线优化**: 重叠计算和内存访问

### 3. 并行优化
- **线程块大小**: 根据GPU架构选择最优块大小
- **网格配置**: 平衡负载和资源利用率
- **占用率优化**: 最大化SM利用率

## 🔧 参数调优

### GPU架构适配

不同GPU架构的推荐配置：

| GPU架构 | 推荐块大小 | 网格因子 | 共享内存 |
|---------|-----------|----------|----------|
| Pascal (GTX 10xx) | 256 | 2 | 是 |
| Turing (RTX 20xx) | 512 | 2 | 是 |
| Ampere (RTX 30xx) | 1024 | 1 | 否 |

### 数据规模适配

| 数据大小 | 推荐算法 | 优化策略 |
|----------|----------|----------|
| n < 1K | Radix-2 | 高线程利用率 |
| 1K ≤ n < 16K | Radix-4 | 预计算+共享内存 |
| n ≥ 16K | 混合Radix | 内存带宽优化 |

## 🧪 测试验证

### 正确性验证
所有实现都通过`nttdata/`目录下的标准测试用例验证，确保算法正确性。

### 性能基准
- **基准对比**: 与CPU实现对比加速比
- **算法对比**: 不同CUDA实现之间的性能对比
- **参数优化**: 不同超参数配置的性能影响

## 📈 预期性能

在典型的现代GPU上，相比CPU实现的预期加速比：

- **Radix-2 基础**: 10-50x
- **Radix-4 优化**: 20-100x  
- **Barrett/Montgomery**: 30-150x
- **混合优化**: 50-200x

*实际性能取决于具体的GPU型号、数据大小和系统配置*

## 🤝 贡献指南

1. 确保所有新实现都通过测试用例
2. 添加详细的性能分析
3. 更新相关文档
4. 遵循现有的代码风格

## 📚 参考资料

- [CUDA Programming Guide](https://docs.nvidia.com/cuda/cuda-c-programming-guide/)
- [Number Theoretic Transform](https://en.wikipedia.org/wiki/Discrete_Fourier_transform_(general))
- [Barrett Reduction](https://en.wikipedia.org/wiki/Barrett_reduction)
- [Montgomery Modular Multiplication](https://en.wikipedia.org/wiki/Montgomery_modular_multiplication)

## 📄 许可证

本项目遵循MIT许可证，详见LICENSE文件。
