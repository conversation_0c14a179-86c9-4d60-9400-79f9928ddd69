#!/bin/bash

# HIP GPU 性能优化测试脚本
echo "============================"
echo "HIP GPU 性能优化测试脚本"
echo "============================"

# 检查hipcc编译器是否可用
if ! command -v hipcc &> /dev/null; then
    echo "错误: hipcc编译器未找到，请确保HIP环境已正确安装"
    exit 1
fi

echo "正在检查HIP环境..."
hipcc --version

echo -e "\n编译优化标志说明:"
echo "  -O3: 最高级别优化"
echo "  -mcumode: GPU计算模式"
echo "  -ffast-math: 快速数学运算"
echo "  -funroll-loops: 循环展开"

# 编译原始版本
echo -e "\n正在编译原始版本..."
if [ -f "temp/main-3.cpp" ]; then
    hipcc -O2 temp/main-3.cpp -o mlp_original
    if [ $? -eq 0 ]; then
        echo "✓ 原始版本编译成功: mlp_original"
    else
        echo "✗ 原始版本编译失败"
    fi
else
    echo "⚠ 原始版本文件 temp/main-3.cpp 未找到"
fi

# 编译优化版本
echo -e "\n正在编译优化版本..."
if [ -f "main-3-optimized.cpp" ]; then
    hipcc -O3 -mcumode -ffast-math -funroll-loops main-3-optimized.cpp -o mlp_optimized
    if [ $? -eq 0 ]; then
        echo "✓ 优化版本编译成功: mlp_optimized"
    else
        echo "✗ 优化版本编译失败"
    fi
else
    echo "✗ 优化版本文件 main-3-optimized.cpp 未找到"
    exit 1
fi

# 编译性能基准测试
echo -e "\n正在编译性能基准测试..."
if [ -f "performance_benchmark.cpp" ]; then
    hipcc -O3 -mcumode -ffast-math -funroll-loops performance_benchmark.cpp -o benchmark
    if [ $? -eq 0 ]; then
        echo "✓ 性能基准测试编译成功: benchmark"
    else
        echo "✗ 性能基准测试编译失败"
    fi
else
    echo "✗ 性能基准测试文件 performance_benchmark.cpp 未找到"
fi

echo -e "\n编译总结:"
echo "===================="
ls -la mlp_* benchmark 2>/dev/null | grep -E '^-rwx' || echo "没有找到可执行文件"

# 询问是否运行测试
echo -e "\n是否运行性能基准测试? (y/n)"
read -r response
if [[ "$response" =~ ^[Yy]$ ]]; then
    echo -e "\n正在运行性能基准测试..."
    echo "============================"
    if [ -f "./benchmark" ]; then
        ./benchmark
    else
        echo "✗ benchmark 可执行文件未找到"
    fi
fi

# 检查数据文件
echo -e "\n检查数据文件..."
if [ -f "bandwidth_data.json" ]; then
    echo "✓ 数据文件 bandwidth_data.json 存在"
    echo "是否运行优化版MLP训练? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo -e "\n正在运行优化版MLP训练..."
        echo "================================"
        if [ -f "./mlp_optimized" ]; then
            ./mlp_optimized
        else
            echo "✗ mlp_optimized 可执行文件未找到"
        fi
    fi
else
    echo "⚠ 数据文件 bandwidth_data.json 不存在"
    echo "  创建示例数据文件..."
    
    # 创建示例数据文件
    cat > bandwidth_data.json << EOF
[1.2, 1.5, 1.3, 1.8, 2.1, 1.9, 2.3, 2.0, 1.7, 1.4, 1.6, 1.8, 2.2, 2.4, 2.1, 1.9, 1.5, 1.3, 1.7, 2.0, 2.2, 2.5, 2.3, 2.1, 1.8, 1.6, 1.4, 1.7, 2.0, 2.3, 2.1, 1.9, 1.6, 1.4, 1.8, 2.1, 2.4, 2.2, 2.0, 1.7, 1.5, 1.8, 2.1, 2.3, 2.0, 1.8, 1.6, 1.9, 2.2, 2.4]
EOF
    
    echo "✓ 示例数据文件已创建"
    
    echo "是否运行优化版MLP训练? (y/n)"
    read -r response
    if [[ "$response" =~ ^[Yy]$ ]]; then
        echo -e "\n正在运行优化版MLP训练..."
        echo "================================"
        if [ -f "./mlp_optimized" ]; then
            ./mlp_optimized
        else
            echo "✗ mlp_optimized 可执行文件未找到"
        fi
    fi
fi

echo -e "\n优化建议："
echo "============"
echo "1. 如果您的GPU支持更高的共享内存，可以增加TILE_SIZE"
echo "2. 根据您的具体GPU架构调整WARP_SIZE"
echo "3. 对于更大的网络，考虑使用混合精度计算"
echo "4. 使用GPU profiler (如hipprof)来进一步分析性能"

echo -e "\n使用hipprof进行性能分析："
echo "hipprof ./mlp_optimized"
echo "hipprof ./benchmark"

echo -e "\n测试脚本执行完成!" 