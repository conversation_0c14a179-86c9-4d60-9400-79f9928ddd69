#!/bin/bash

# Exit on error
set -e

# --- Configuration ---
# Executables and their source files
declare -A EXECUTABLES
EXECUTABLES=(
    ["r2_scalar_vs_mpi"]="mpi/main_barrett_radix2_ntt.cc"
    ["r2_scalar_vs_simd_mpi"]="mpi/main_barrett_radix2_simd_mpi_fixed.cc"
    ["r4_scalar_mpi"]="mpi/main_barrett_radix4_ntt.cc"
    ["r4_scalar_vs_simd_mpi"]="mpi/main_barrett_radix4_simd_mpi.cc"
)

# Number of MPI processes to test
PROCESSES=(1 2 4 8)

# Output CSV file
RESULTS_FILE="results.csv"

# Compiler and flags
MPICXX="mpicxx"
CXXFLAGS="-std=c++17 -O3 -march=native -Wall"

# --- <PERSON>ript Logic ---

echo "--- Starting Performance Experiment ---"

# 1. Compilation
echo "--- Compiling source files... ---"
for exe in "${!EXECUTABLES[@]}"; do
    src=${EXECUTABLES[$exe]}
    echo "Compiling $src -> bin/$exe"
    mkdir -p bin
    $MPICXX $CXXFLAGS -o "bin/$exe" "$src"
done
echo "--- Compilation finished. ---"
echo

# 2. Data Collection
echo "--- Running benchmarks and collecting data... ---"
# Create CSV header
echo "algorithm,test_case_id,n,p,processes,time_us" > $RESULTS_FILE

# Run experiments
for p in "${PROCESSES[@]}"; do
    echo "Running with $p processes..."

    # Test 1: Radix-2 Scalar Serial vs MPI
    echo "  Executing r2_scalar_vs_mpi..."
    mpirun -np "$p" bin/r2_scalar_vs_mpi | awk -v p="$p" '
        /测试用例/ { id=$2; n=$3; sub(/\(n=/, "", n); sub(/,/, "", n); p_val=$4; sub(/p=/, "", p_val); sub(/\):/, "", p_val) }
        /串行NTT:/ { printf "serial_r2_scalar,%s,%s,%s,%d,%.2f\n", id, n, p_val, 1, $3 }
        /并行NTT:/ { printf "mpi_r2_scalar,%s,%s,%s,%d,%.2f\n", id, n, p_val, p, $3 }
    ' >> $RESULTS_FILE

    # Test 2: Radix-2 Scalar MPI vs SIMD MPI
    echo "  Executing r2_scalar_vs_simd_mpi..."
    mpirun -np "$p" bin/r2_scalar_vs_simd_mpi | awk -v p="$p" '
        /测试用例/ { id=$2; n=$3; sub(/\(n=/, "", n); sub(/,/, "", n); p_val=$4; sub(/p=/, "", p_val); sub(/\):/, "", p_val) }
        /标量NTT:/ { printf "mpi_r2_scalar_v2,%s,%s,%s,%d,%.2f\n", id, n, p_val, p, $3 }
        /SIMD NTT:/ { printf "mpi_r2_simd,%s,%s,%s,%d,%.2f\n", id, n, p_val, p, $3 }
    ' >> $RESULTS_FILE

    # Test 3: Radix-4 Scalar MPI
    echo "  Executing r4_scalar_mpi..."
    mpirun -np "$p" bin/r4_scalar_mpi | awk -v p="$p" '
        /id=/ {
            for(i=1; i<=NF; ++i) {
                if($i ~ /^id=/) { id=substr($i, 4) }
                if($i ~ /^n=/) { n=substr($i, 3) }
                if($i ~ /^p=/) { p_val=substr($i, 3) }
                if($i ~ /^time=/) { time=substr($i, 6) }
            }
            printf "mpi_r4_scalar,%s,%s,%s,%d,%.2f\n", id, n, p_val, p, time
        }
    ' >> $RESULTS_FILE

    # Test 4: Radix-4 Scalar MPI vs SIMD MPI
    echo "  Executing r4_scalar_vs_simd_mpi..."
    mpirun -np "$p" bin/r4_scalar_vs_simd_mpi | awk -v p="$p" '
        /测试用例/ { id=$2; n=$3; sub(/\(n=/, "", n); sub(/,/, "", n); p_val=$4; sub(/p=/, "", p_val); sub(/\):/, "", p_val) }
        /标量NTT:/ { printf "mpi_r4_scalar_v2,%s,%s,%s,%d,%.2f\n", id, n, p_val, p, $3 }
        /SIMD NTT:/ { printf "mpi_r4_simd,%s,%s,%s,%d,%.2f\n", id, n, p_val, p, $3 }
    ' >> $RESULTS_FILE

done
echo "--- Benchmarking finished. Results are in $RESULTS_FILE ---"
echo
echo "--- Experiment Complete ---" 