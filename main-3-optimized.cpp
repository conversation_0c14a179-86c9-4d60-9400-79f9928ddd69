#include <hip/hip_runtime.h>
#include <iostream>
#include <fstream>
#include <vector>
#include <cmath>
#include <chrono>
#include <string>
#include <sstream>
#include <algorithm>

// 编译文件: hipcc -O3 -mcumode -ffast-math -funroll-loops main-3-optimized.cpp -o mlp_optimized
// 执行文件: ./mlp_optimized 或者 hipprof ./mlp_optimized

// 预定义参数
#define INPUT_DIM 10
#define HIDDEN_DIM 32
#define OUTPUT_DIM 1
#define BATCH_SIZE 256
#define EPOCHS 200
#define LEARNING_RATE 1e-4

// 优化参数
#define TILE_SIZE 16
#define REGISTER_TILE_M 4
#define REGISTER_TILE_N 4
#define WARP_SIZE 64

#define HIP_CHECK(error) \
    do { \
        hipError_t err = error; \
        if (err != hipSuccess) { \
            fprintf(stderr, "HIP error (%s:%d): %s\n", __FILE__, __LINE__, hipGetErrorString(err)); \
            exit(EXIT_FAILURE); \
        } \
    } while (0)

/**
 * @brief 优化的平铺矩阵乘法内核，使用共享内存和寄存器平铺
 * @param A 输入矩阵A指针
 * @param B 输入矩阵B指针  
 * @param C 输出矩阵C指针
 * @param M 矩阵A的行数
 * @param N 矩阵B的列数
 * @param K 矩阵A的列数/矩阵B的行数
 */
__global__ void optimized_matmul(const double* __restrict__ A, 
                                const double* __restrict__ B, 
                                double* __restrict__ C, 
                                int M, int N, int K) {
    __shared__ double As[TILE_SIZE][TILE_SIZE + 1];
    __shared__ double Bs[TILE_SIZE][TILE_SIZE + 1];
    
    const int tx = threadIdx.x;
    const int ty = threadIdx.y;
    const int bx = blockIdx.x;
    const int by = blockIdx.y;
    
    const int row = by * TILE_SIZE + ty;
    const int col = bx * TILE_SIZE + tx;
    
    double c_reg[REGISTER_TILE_M][REGISTER_TILE_N] = {0.0};
    
    #pragma unroll
    for (int tile_idx = 0; tile_idx < (K + TILE_SIZE - 1) / TILE_SIZE; ++tile_idx) {
        int k_start = tile_idx * TILE_SIZE;
        
        if (row < M && (k_start + tx) < K) {
            As[ty][tx] = A[row * K + k_start + tx];
        } else {
            As[ty][tx] = 0.0;
        }
        
        if ((k_start + ty) < K && col < N) {
            Bs[ty][tx] = B[(k_start + ty) * N + col];
        } else {
            Bs[ty][tx] = 0.0;
        }
        
        __syncthreads();
        
        #pragma unroll
        for (int k = 0; k < TILE_SIZE; ++k) {
            #pragma unroll
            for (int rm = 0; rm < REGISTER_TILE_M; ++rm) {
                #pragma unroll
                for (int rn = 0; rn < REGISTER_TILE_N; ++rn) {
                    c_reg[rm][rn] += As[ty + rm * blockDim.y][k] * 
                                   Bs[k][tx + rn * blockDim.x];
                }
            }
        }
        
        __syncthreads();
    }
    
    #pragma unroll
    for (int rm = 0; rm < REGISTER_TILE_M; ++rm) {
        #pragma unroll
        for (int rn = 0; rn < REGISTER_TILE_N; ++rn) {
            int global_row = row + rm * blockDim.y;
            int global_col = col + rn * blockDim.x;
            if (global_row < M && global_col < N) {
                C[global_row * N + global_col] = c_reg[rm][rn];
            }
        }
    }
}

/**
 * @brief 向量化ReLU激活函数，使用寄存器级并行
 * @param x 输入输出数组指针
 * @param size 数组大小
 */
__global__ void vectorized_relu_forward(double* __restrict__ x, int size) {
    const int idx = blockIdx.x * blockDim.x + threadIdx.x;
    const int stride = gridDim.x * blockDim.x;
    
    #pragma unroll 4
    for (int i = idx; i < size; i += stride) {
        x[i] = fmax(0.0, x[i]);
    }
}

/**
 * @brief 高效的输出梯度计算内核
 * @param pred 预测值数组
 * @param target 目标值数组
 * @param grad 梯度输出数组
 * @param size 数组大小
 */
__global__ void efficient_compute_output_grad(const double* __restrict__ pred, 
                                             const double* __restrict__ target, 
                                             double* __restrict__ grad, 
                                             int size) {
    const int idx = blockIdx.x * blockDim.x + threadIdx.x;
    const int stride = gridDim.x * blockDim.x;
    
    #pragma unroll 4
    for (int i = idx; i < size; i += stride) {
        grad[i] = pred[i] - target[i];
    }
}

/**
 * @brief 高效的ReLU反向传播内核
 * @param delta 梯度数组
 * @param activ 激活值数组
 * @param size 数组大小
 */
__global__ void efficient_relu_backward(double* __restrict__ delta, 
                                       const double* __restrict__ activ, 
                                       int size) {
    const int idx = blockIdx.x * blockDim.x + threadIdx.x;
    const int stride = gridDim.x * blockDim.x;
    
    #pragma unroll 4
    for (int i = idx; i < size; i += stride) {
        if (activ[i] <= 0.0) {
            delta[i] = 0.0;
        }
    }
}

/**
 * @brief 优化的权重梯度计算内核
 * @param upstream_grad 上游梯度
 * @param layer_input 层输入
 * @param weight_grad 权重梯度输出
 * @param M 批大小
 * @param N 输入维度
 * @param K 输出维度
 */
__global__ void optimized_matmul_backward_weights(const double* __restrict__ upstream_grad, 
                                                 const double* __restrict__ layer_input, 
                                                 double* __restrict__ weight_grad, 
                                                 int M, int N, int K) {
    __shared__ double As[TILE_SIZE][TILE_SIZE + 1];
    __shared__ double Bs[TILE_SIZE][TILE_SIZE + 1];
    
    const int tx = threadIdx.x;
    const int ty = threadIdx.y;
    const int bx = blockIdx.x;
    const int by = blockIdx.y;
    
    const int row = by * TILE_SIZE + ty;
    const int col = bx * TILE_SIZE + tx;
    
    double sum = 0.0;
    
    #pragma unroll
    for (int tile_idx = 0; tile_idx < (M + TILE_SIZE - 1) / TILE_SIZE; ++tile_idx) {
        int m_start = tile_idx * TILE_SIZE;
        
        if (row < N && (m_start + tx) < M) {
            As[ty][tx] = layer_input[(m_start + tx) * N + row];
        } else {
            As[ty][tx] = 0.0;
        }
        
        if ((m_start + ty) < M && col < K) {
            Bs[ty][tx] = upstream_grad[(m_start + ty) * K + col];
        } else {
            Bs[ty][tx] = 0.0;
        }
        
        __syncthreads();
        
        #pragma unroll
        for (int k = 0; k < TILE_SIZE; ++k) {
            sum += As[ty][k] * Bs[k][tx];
        }
        
        __syncthreads();
    }
    
    if (row < N && col < K) {
        weight_grad[row * K + col] = sum;
    }
}

/**
 * @brief 优化的输入梯度计算内核
 * @param upstream_grad 上游梯度
 * @param weights 权重矩阵
 * @param input_grad 输入梯度输出
 * @param M 批大小
 * @param N 输入维度
 * @param K 输出维度
 */
__global__ void optimized_matmul_backward_input(const double* __restrict__ upstream_grad, 
                                               const double* __restrict__ weights, 
                                               double* __restrict__ input_grad, 
                                               int M, int N, int K) {
    __shared__ double As[TILE_SIZE][TILE_SIZE + 1];
    __shared__ double Bs[TILE_SIZE][TILE_SIZE + 1];
    
    const int tx = threadIdx.x;
    const int ty = threadIdx.y;
    const int bx = blockIdx.x;
    const int by = blockIdx.y;
    
    const int row = by * TILE_SIZE + ty;
    const int col = bx * TILE_SIZE + tx;
    
    double sum = 0.0;
    
    #pragma unroll
    for (int tile_idx = 0; tile_idx < (K + TILE_SIZE - 1) / TILE_SIZE; ++tile_idx) {
        int k_start = tile_idx * TILE_SIZE;
        
        if (row < M && (k_start + tx) < K) {
            As[ty][tx] = upstream_grad[row * K + k_start + tx];
        } else {
            As[ty][tx] = 0.0;
        }
        
        if ((k_start + ty) < K && col < N) {
            Bs[ty][tx] = weights[col * K + k_start + ty];
        } else {
            Bs[ty][tx] = 0.0;
        }
        
        __syncthreads();
        
        #pragma unroll
        for (int k = 0; k < TILE_SIZE; ++k) {
            sum += As[ty][k] * Bs[k][tx];
        }
        
        __syncthreads();
    }
    
    if (row < M && col < N) {
        input_grad[row * N + col] = sum;
    }
}

/**
 * @brief 高效的MSE损失计算内核
 * @param pred 预测值
 * @param target 目标值
 * @param loss 损失输出
 * @param size 数组大小
 */
__global__ void efficient_mse_loss(const double* __restrict__ pred, 
                                  const double* __restrict__ target, 
                                  double* __restrict__ loss, 
                                  int size) {
    const int idx = blockIdx.x * blockDim.x + threadIdx.x;
    const int stride = gridDim.x * blockDim.x;
    
    #pragma unroll 4
    for (int i = idx; i < size; i += stride) {
        double diff = pred[i] - target[i];
        loss[i] = 0.5 * diff * diff;
    }
}

/**
 * @brief 向量化SGD更新内核
 * @param weights 权重数组
 * @param grad 梯度数组
 * @param lr 学习率
 * @param size 数组大小
 */
__global__ void vectorized_sgd_update(double* __restrict__ weights, 
                                     const double* __restrict__ grad, 
                                     double lr, 
                                     int size) {
    const int idx = blockIdx.x * blockDim.x + threadIdx.x;
    const int stride = gridDim.x * blockDim.x;
    
    #pragma unroll 4
    for (int i = idx; i < size; i += stride) {
        weights[i] -= lr * grad[i];
    }
}

/**
 * @brief 高效的偏置添加内核
 * @param output 输出矩阵
 * @param bias 偏置向量
 * @param M 批大小
 * @param N 特征维度
 */
__global__ void efficient_add_bias(double* __restrict__ output, 
                                  const double* __restrict__ bias, 
                                  int M, int N) {
    const int row = blockIdx.y * blockDim.y + threadIdx.y;
    const int col = blockIdx.x * blockDim.x + threadIdx.x;
    const int col_stride = gridDim.x * blockDim.x;
    const int row_stride = gridDim.y * blockDim.y;
    
    for (int r = row; r < M; r += row_stride) {
        #pragma unroll 4
        for (int c = col; c < N; c += col_stride) {
            output[r * N + c] += bias[c];
        }
    }
}

/**
 * @brief 使用warp级reduction的偏置梯度计算
 * @param delta 激活梯度
 * @param bias_grad 偏置梯度输出
 * @param batch_size 批大小
 * @param num_neurons 神经元数量
 */
__global__ void warp_reduce_bias_gradients(const double* __restrict__ delta, 
                                          double* __restrict__ bias_grad, 
                                          int batch_size, 
                                          int num_neurons) {
    const int neuron_idx = blockIdx.x * blockDim.x + threadIdx.x;
    const int lane_id = threadIdx.x % WARP_SIZE;
    
    if (neuron_idx < num_neurons) {
        double sum = 0.0;
        
        for (int i = 0; i < batch_size; ++i) {
            sum += delta[i * num_neurons + neuron_idx];
        }
        
        // Warp级reduction
        #pragma unroll
        for (int offset = WARP_SIZE / 2; offset > 0; offset /= 2) {
            sum += __shfl_down(sum, offset);
        }
        
        if (lane_id == 0) {
            bias_grad[neuron_idx] = sum / batch_size;
        }
    }
}

// 加载带宽数据
std::vector<double> load_json_bandwidth(const std::string& filename) {
    std::vector<double> bandwidth_data;
    std::ifstream file(filename);
    if (!file.is_open()) {
        std::cerr << "Error: Could not open file " << filename << std::endl;
        return bandwidth_data;
    }

    std::string content;
    std::string line;
    while (std::getline(file, line)) {
        content += line;
    }
    file.close();

    content.erase(std::remove_if(content.begin(), content.end(), ::isspace), content.end());
    
    size_t start_pos = content.find('[');
    size_t end_pos = content.rfind(']');

    if (start_pos == std::string::npos || end_pos == std::string::npos || start_pos >= end_pos) {
        std::cerr << "Error: Invalid JSON array format in " << filename << std::endl;
        return bandwidth_data;
    }

    std::string array_content = content.substr(start_pos + 1, end_pos - start_pos - 1);
    std::stringstream ss(array_content);
    std::string item;

    while (std::getline(ss, item, ',')) {
        try {
            bandwidth_data.push_back(std::stod(item));
        } catch (const std::invalid_argument& ia) {
            std::cerr << "Invalid argument: " << ia.what() << " for item: " << item << std::endl;
        } catch (const std::out_of_range& oor) {
            std::cerr << "Out of Range error: " << oor.what() << " for item: " << item << std::endl;
        }
    }
    return bandwidth_data;
}

void create_dataset(const std::vector<double>& data,
                    std::vector<double>& X,
                    std::vector<double>& y,
                    int window_size) {
    X.clear();
    y.clear();
    if (data.size() <= window_size) {
        std::cerr << "Error: Data size is too small for the given window size." << std::endl;
        return;
    }

    for (size_t i = 0; i < data.size() - window_size; ++i) {
        for (int j = 0; j < window_size; ++j) {
            X.push_back(data[i + j]);
        }
        y.push_back(data[i + window_size]);
    }
}

void normalize_data(std::vector<double>& data, double& min_val, double& max_val) {
    auto minmax = std::minmax_element(data.begin(), data.end());
    min_val = *minmax.first;
    max_val = *minmax.second;
    for (auto& val : data) {
        val = (val - min_val) / (max_val - min_val);
    }
}

void denormalize_data(std::vector<double>& data, double min_val, double max_val) {
    for (auto& val : data) {
        val = val * (max_val - min_val) + min_val;
    }
}

/**
 * @brief 计算优化的网格和块维度
 * @param M 矩阵行数
 * @param N 矩阵列数
 * @return 配置的dim3网格和块维度
 */
std::pair<dim3, dim3> calculate_optimized_launch_config(int M, int N) {
    dim3 block_dim(TILE_SIZE, TILE_SIZE);
    dim3 grid_dim((N + TILE_SIZE - 1) / TILE_SIZE, (M + TILE_SIZE - 1) / TILE_SIZE);
    return {grid_dim, block_dim};
}

/**
 * @brief 性能测量工具
 */
class PerformanceTimer {
private:
    std::chrono::high_resolution_clock::time_point start_time;
    std::chrono::high_resolution_clock::time_point end_time;
    
public:
    void start() {
        start_time = std::chrono::high_resolution_clock::now();
    }
    
    double stop() {
        end_time = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end_time - start_time);
        return duration.count() / 1000.0; // 返回毫秒
    }
    
    double calculate_gflops(long long operations, double time_ms) {
        return (operations / 1e9) / (time_ms / 1000.0);
    }
};

int main() {
    std::cout << "开始优化版MLP训练..." << std::endl;
    
    PerformanceTimer timer;
    
    // 数据加载
    std::string filename = "bandwidth_data.json";
    std::vector<double> raw_data = load_json_bandwidth(filename);
    if (raw_data.empty()) {
        std::cerr << "Failed to load data from " << filename << std::endl;
        return -1;
    }

    std::cout << "Loaded " << raw_data.size() << " data points from " << filename << std::endl;

    double min_val, max_val;
    normalize_data(raw_data, min_val, max_val);

    std::vector<double> X, y;
    create_dataset(raw_data, X, y, INPUT_DIM);

    int num_samples = y.size();
    if (num_samples == 0) {
        std::cerr << "No samples created. Check your data and window size." << std::endl;
        return -1;
    }

    std::cout << "Created " << num_samples << " samples with window size " << INPUT_DIM << std::endl;

    int train_samples = static_cast<int>(0.8 * num_samples);
    int test_samples = num_samples - train_samples;

    std::cout << "Training samples: " << train_samples << ", Test samples: " << test_samples << std::endl;

    // 权重初始化
    std::vector<double> h_weights_ih(INPUT_DIM * HIDDEN_DIM);
    std::vector<double> h_bias_ih(HIDDEN_DIM);
    std::vector<double> h_weights_ho(HIDDEN_DIM * OUTPUT_DIM);
    std::vector<double> h_bias_ho(OUTPUT_DIM);

    auto init_weights = [](std::vector<double>& vec, int in_dim, int out_dim) {
        double scale = std::sqrt(2.0 / in_dim); // He初始化
        for (auto& w : vec) {
            w = scale * ((double)rand() / RAND_MAX * 2.0 - 1.0);
        }
    };

    init_weights(h_weights_ih, INPUT_DIM, HIDDEN_DIM);
    init_weights(h_weights_ho, HIDDEN_DIM, OUTPUT_DIM);

    // GPU内存分配
    double *d_weights_ih, *d_bias_ih, *d_weights_ho, *d_bias_ho;
    double *d_input, *d_hidden, *d_output, *d_target;
    double *d_delta_output, *d_delta_hidden;
    double *d_grad_weights_ih, *d_grad_bias_ih, *d_grad_weights_ho, *d_grad_bias_ho;

    size_t input_size = BATCH_SIZE * INPUT_DIM * sizeof(double);
    size_t hidden_size = BATCH_SIZE * HIDDEN_DIM * sizeof(double);
    size_t output_size = BATCH_SIZE * OUTPUT_DIM * sizeof(double);

    HIP_CHECK(hipMalloc(&d_weights_ih, INPUT_DIM * HIDDEN_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_bias_ih, HIDDEN_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_weights_ho, HIDDEN_DIM * OUTPUT_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_bias_ho, OUTPUT_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_input, input_size));
    HIP_CHECK(hipMalloc(&d_hidden, hidden_size));
    HIP_CHECK(hipMalloc(&d_output, output_size));
    HIP_CHECK(hipMalloc(&d_target, output_size));
    HIP_CHECK(hipMalloc(&d_delta_output, output_size));
    HIP_CHECK(hipMalloc(&d_delta_hidden, hidden_size));
    HIP_CHECK(hipMalloc(&d_grad_weights_ih, INPUT_DIM * HIDDEN_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_grad_bias_ih, HIDDEN_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_grad_weights_ho, HIDDEN_DIM * OUTPUT_DIM * sizeof(double)));
    HIP_CHECK(hipMalloc(&d_grad_bias_ho, OUTPUT_DIM * sizeof(double)));

    // 复制权重到GPU
    HIP_CHECK(hipMemcpy(d_weights_ih, h_weights_ih.data(), INPUT_DIM * HIDDEN_DIM * sizeof(double), hipMemcpyHostToDevice));
    HIP_CHECK(hipMemcpy(d_bias_ih, h_bias_ih.data(), HIDDEN_DIM * sizeof(double), hipMemcpyHostToDevice));
    HIP_CHECK(hipMemcpy(d_weights_ho, h_weights_ho.data(), HIDDEN_DIM * OUTPUT_DIM * sizeof(double), hipMemcpyHostToDevice));
    HIP_CHECK(hipMemcpy(d_bias_ho, h_bias_ho.data(), OUTPUT_DIM * sizeof(double), hipMemcpyHostToDevice));

    std::cout << "开始训练..." << std::endl;
    
    timer.start();
    double total_training_time = 0.0;

    // 训练循环
    for (int epoch = 0; epoch < EPOCHS; ++epoch) {
        double epoch_loss = 0.0;
        int num_batches = (train_samples + BATCH_SIZE - 1) / BATCH_SIZE;

        for (int batch = 0; batch < num_batches; ++batch) {
            int start_idx = batch * BATCH_SIZE;
            int end_idx = std::min(start_idx + BATCH_SIZE, train_samples);
            int current_batch_size = end_idx - start_idx;

            if (current_batch_size == 0) break;

            // 准备批次数据
            std::vector<double> h_batch_input(current_batch_size * INPUT_DIM);
            std::vector<double> h_batch_target(current_batch_size * OUTPUT_DIM);

            for (int i = 0; i < current_batch_size; ++i) {
                for (int j = 0; j < INPUT_DIM; ++j) {
                    h_batch_input[i * INPUT_DIM + j] = X[(start_idx + i) * INPUT_DIM + j];
                }
                h_batch_target[i * OUTPUT_DIM] = y[start_idx + i];
            }

            // 复制到GPU
            HIP_CHECK(hipMemcpy(d_input, h_batch_input.data(), current_batch_size * INPUT_DIM * sizeof(double), hipMemcpyHostToDevice));
            HIP_CHECK(hipMemcpy(d_target, h_batch_target.data(), current_batch_size * OUTPUT_DIM * sizeof(double), hipMemcpyHostToDevice));

            // 前向传播 - 隐藏层
            auto [grid_ih, block_ih] = calculate_optimized_launch_config(current_batch_size, HIDDEN_DIM);
            optimized_matmul<<<grid_ih, block_ih>>>(d_input, d_weights_ih, d_hidden, current_batch_size, HIDDEN_DIM, INPUT_DIM);
            
            dim3 bias_grid((HIDDEN_DIM + 15) / 16, (current_batch_size + 15) / 16);
            dim3 bias_block(16, 16);
            efficient_add_bias<<<bias_grid, bias_block>>>(d_hidden, d_bias_ih, current_batch_size, HIDDEN_DIM);
            
            int relu_blocks = (current_batch_size * HIDDEN_DIM + 255) / 256;
            vectorized_relu_forward<<<relu_blocks, 256>>>(d_hidden, current_batch_size * HIDDEN_DIM);

            // 前向传播 - 输出层
            auto [grid_ho, block_ho] = calculate_optimized_launch_config(current_batch_size, OUTPUT_DIM);
            optimized_matmul<<<grid_ho, block_ho>>>(d_hidden, d_weights_ho, d_output, current_batch_size, OUTPUT_DIM, HIDDEN_DIM);
            
            efficient_add_bias<<<dim3((OUTPUT_DIM + 15) / 16, (current_batch_size + 15) / 16), dim3(16, 16)>>>(d_output, d_bias_ho, current_batch_size, OUTPUT_DIM);

            // 计算损失
            std::vector<double> h_batch_loss(current_batch_size * OUTPUT_DIM);
            int loss_blocks = (current_batch_size * OUTPUT_DIM + 255) / 256;
            efficient_mse_loss<<<loss_blocks, 256>>>(d_output, d_target, d_delta_output, current_batch_size * OUTPUT_DIM);

            // 反向传播 - 输出层梯度
            efficient_compute_output_grad<<<loss_blocks, 256>>>(d_output, d_target, d_delta_output, current_batch_size * OUTPUT_DIM);

            // 反向传播 - 隐藏层权重梯度
            auto [grad_grid_ho, grad_block_ho] = calculate_optimized_launch_config(HIDDEN_DIM, OUTPUT_DIM);
            optimized_matmul_backward_weights<<<grad_grid_ho, grad_block_ho>>>(d_delta_output, d_hidden, d_grad_weights_ho, current_batch_size, HIDDEN_DIM, OUTPUT_DIM);

            // 偏置梯度
            int bias_grad_blocks = (OUTPUT_DIM + WARP_SIZE - 1) / WARP_SIZE;
            warp_reduce_bias_gradients<<<bias_grad_blocks, WARP_SIZE>>>(d_delta_output, d_grad_bias_ho, current_batch_size, OUTPUT_DIM);

            // 反向传播 - 隐藏层
            auto [grad_grid_hidden, grad_block_hidden] = calculate_optimized_launch_config(current_batch_size, HIDDEN_DIM);
            optimized_matmul_backward_input<<<grad_grid_hidden, grad_block_hidden>>>(d_delta_output, d_weights_ho, d_delta_hidden, current_batch_size, HIDDEN_DIM, OUTPUT_DIM);

            int hidden_blocks = (current_batch_size * HIDDEN_DIM + 255) / 256;
            efficient_relu_backward<<<hidden_blocks, 256>>>(d_delta_hidden, d_hidden, current_batch_size * HIDDEN_DIM);

            // 输入层权重梯度
            auto [grad_grid_ih, grad_block_ih] = calculate_optimized_launch_config(INPUT_DIM, HIDDEN_DIM);
            optimized_matmul_backward_weights<<<grad_grid_ih, grad_block_ih>>>(d_delta_hidden, d_input, d_grad_weights_ih, current_batch_size, INPUT_DIM, HIDDEN_DIM);

            bias_grad_blocks = (HIDDEN_DIM + WARP_SIZE - 1) / WARP_SIZE;
            warp_reduce_bias_gradients<<<bias_grad_blocks, WARP_SIZE>>>(d_delta_hidden, d_grad_bias_ih, current_batch_size, HIDDEN_DIM);

            // 权重更新
            int weight_ih_blocks = (INPUT_DIM * HIDDEN_DIM + 255) / 256;
            vectorized_sgd_update<<<weight_ih_blocks, 256>>>(d_weights_ih, d_grad_weights_ih, LEARNING_RATE, INPUT_DIM * HIDDEN_DIM);

            int bias_ih_blocks = (HIDDEN_DIM + 255) / 256;
            vectorized_sgd_update<<<bias_ih_blocks, 256>>>(d_bias_ih, d_grad_bias_ih, LEARNING_RATE, HIDDEN_DIM);

            int weight_ho_blocks = (HIDDEN_DIM * OUTPUT_DIM + 255) / 256;
            vectorized_sgd_update<<<weight_ho_blocks, 256>>>(d_weights_ho, d_grad_weights_ho, LEARNING_RATE, HIDDEN_DIM * OUTPUT_DIM);

            int bias_ho_blocks = (OUTPUT_DIM + 255) / 256;
            vectorized_sgd_update<<<bias_ho_blocks, 256>>>(d_bias_ho, d_grad_bias_ho, LEARNING_RATE, OUTPUT_DIM);

            HIP_CHECK(hipDeviceSynchronize());

            // 计算当前批次损失
            HIP_CHECK(hipMemcpy(h_batch_loss.data(), d_delta_output, current_batch_size * OUTPUT_DIM * sizeof(double), hipMemcpyDeviceToHost));
            for (double loss_val : h_batch_loss) {
                epoch_loss += loss_val;
            }
        }

        if (epoch % 20 == 0) {
            std::cout << "Epoch " << epoch << ", Loss: " << epoch_loss / train_samples << std::endl;
        }
    }

    total_training_time = timer.stop();
    std::cout << "训练完成! 总时间: " << total_training_time << " ms" << std::endl;

    // 测试阶段
    std::cout << "开始测试..." << std::endl;
    timer.start();

    std::vector<double> h_predictions(test_samples * OUTPUT_DIM);
    int test_batches = (test_samples + BATCH_SIZE - 1) / BATCH_SIZE;

    for (int batch = 0; batch < test_batches; ++batch) {
        int start_idx = train_samples + batch * BATCH_SIZE;
        int end_idx = std::min(start_idx + BATCH_SIZE, train_samples + test_samples);
        int current_batch_size = end_idx - start_idx;

        if (current_batch_size == 0) break;

        std::vector<double> h_test_input(current_batch_size * INPUT_DIM);
        for (int i = 0; i < current_batch_size; ++i) {
            for (int j = 0; j < INPUT_DIM; ++j) {
                h_test_input[i * INPUT_DIM + j] = X[(start_idx + i) * INPUT_DIM + j];
            }
        }

        HIP_CHECK(hipMemcpy(d_input, h_test_input.data(), current_batch_size * INPUT_DIM * sizeof(double), hipMemcpyHostToDevice));

        // 前向传播
        auto [grid_ih, block_ih] = calculate_optimized_launch_config(current_batch_size, HIDDEN_DIM);
        optimized_matmul<<<grid_ih, block_ih>>>(d_input, d_weights_ih, d_hidden, current_batch_size, HIDDEN_DIM, INPUT_DIM);
        
        efficient_add_bias<<<dim3((HIDDEN_DIM + 15) / 16, (current_batch_size + 15) / 16), dim3(16, 16)>>>(d_hidden, d_bias_ih, current_batch_size, HIDDEN_DIM);
        
        int relu_blocks = (current_batch_size * HIDDEN_DIM + 255) / 256;
        vectorized_relu_forward<<<relu_blocks, 256>>>(d_hidden, current_batch_size * HIDDEN_DIM);

        auto [grid_ho, block_ho] = calculate_optimized_launch_config(current_batch_size, OUTPUT_DIM);
        optimized_matmul<<<grid_ho, block_ho>>>(d_hidden, d_weights_ho, d_output, current_batch_size, OUTPUT_DIM, HIDDEN_DIM);
        
        efficient_add_bias<<<dim3((OUTPUT_DIM + 15) / 16, (current_batch_size + 15) / 16), dim3(16, 16)>>>(d_output, d_bias_ho, current_batch_size, OUTPUT_DIM);

        std::vector<double> h_batch_predictions(current_batch_size * OUTPUT_DIM);
        HIP_CHECK(hipMemcpy(h_batch_predictions.data(), d_output, current_batch_size * OUTPUT_DIM * sizeof(double), hipMemcpyDeviceToHost));

        for (int i = 0; i < current_batch_size; ++i) {
            h_predictions[(batch * BATCH_SIZE + i) * OUTPUT_DIM] = h_batch_predictions[i * OUTPUT_DIM];
        }
    }

    double test_time = timer.stop();
    std::cout << "测试完成! 时间: " << test_time << " ms" << std::endl;

    // 计算测试损失
    double test_loss = 0.0;
    for (int i = 0; i < test_samples; ++i) {
        double diff = h_predictions[i] - y[train_samples + i];
        test_loss += 0.5 * diff * diff;
    }
    test_loss /= test_samples;

    std::cout << "测试损失: " << test_loss << std::endl;

    // 反规一化预测结果进行展示
    denormalize_data(h_predictions, min_val, max_val);
    
    std::cout << "前10个预测结果:" << std::endl;
    for (int i = 0; i < std::min(10, test_samples); ++i) {
        double actual = y[train_samples + i] * (max_val - min_val) + min_val;
        std::cout << "预测: " << h_predictions[i] << ", 实际: " << actual << std::endl;
    }

    // 性能统计
    long long total_ops = (long long)EPOCHS * train_samples * 
                         (2 * INPUT_DIM * HIDDEN_DIM + 2 * HIDDEN_DIM * OUTPUT_DIM);
    double gflops = timer.calculate_gflops(total_ops, total_training_time);
    std::cout << "训练性能: " << gflops << " GFLOPS" << std::endl;

    // 清理
    hipFree(d_weights_ih);
    hipFree(d_bias_ih);
    hipFree(d_weights_ho);
    hipFree(d_bias_ho);
    hipFree(d_input);
    hipFree(d_hidden);
    hipFree(d_output);
    hipFree(d_target);
    hipFree(d_delta_output);
    hipFree(d_delta_hidden);
    hipFree(d_grad_weights_ih);
    hipFree(d_grad_bias_ih);
    hipFree(d_grad_weights_ho);
    hipFree(d_grad_bias_ho);

    std::cout << "优化版MLP训练完成!" << std::endl;
    return 0;
} 