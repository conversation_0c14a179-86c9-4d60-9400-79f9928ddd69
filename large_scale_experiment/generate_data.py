import os
import random

def generate_ntt_data(directory, n, p, file_id):
    """
    Generates a pair of .in and .out files for NTT testing.
    .in file: n p, followed by two polynomials of degree n-1.
    .out file: The result of polynomial multiplication.
    """
    os.makedirs(directory, exist_ok=True)
    
    # --- Generate Input File ---
    in_path = os.path.join(directory, f"{file_id}.in")
    
    # Generate two random polynomials with coefficients in [0, p-1]
    a = [random.randint(0, p - 1) for _ in range(n)]
    b = [random.randint(0, p - 1) for _ in range(n)]

    with open(in_path, 'w') as f:
        f.write(f"{n} {p}\n")
        f.write(" ".join(map(str, a)) + "\n")
        f.write(" ".join(map(str, b)) + "\n")

    # --- Generate Output File (for verification) ---
    # This is a simple, non-NTT based polynomial multiplication for correctness check.
    # It's slow but straightforward.
    out_path = os.path.join(directory, f"{file_id}.out")
    
    # The result polynomial has degree 2n-2, so 2n-1 coefficients
    ab = [0] * (2 * n - 1)
    for i in range(n):
        for j in range(n):
            ab[i + j] = (ab[i + j] + a[i] * b[j]) % p
            
    with open(out_path, 'w') as f:
        f.write(" ".join(map(str, ab)) + "\n")
        
    print(f"Generated data for n={n}, p={p} in {in_path} and {out_path}")


if __name__ == "__main__":
    # --- Configuration ---
    DATA_DIR = "large_scale_experiment/data"
    
    # Suitable prime p = k*n + 1 for NTT.
    # We will test for n up to 2^19. Let's choose a prime that works for all.
    # Let N_max = 2^20 (lim for n=2^19). We need p = k*N_max + 1.
    # Let's find a suitable prime. E.g., for k=3, p = 3 * 2^20 + 1 = 3145729, this is not prime.
    # For k=15, p = 15 * 2^20 + 1 = 15728641, this is prime.
    # Let's pick another one. 998244353 is a popular NTT prime, 998244353-1 = 119 * 2^23.
    # This prime supports n up to 2^23, so it's a great choice.
    NTT_PRIME = 998244353
    
    # Problem sizes to test
    PROBLEM_SIZES = [2**16, 2**17, 2**18, 2**19]
    
    # --- Generation Loop ---
    for i, n_val in enumerate(PROBLEM_SIZES):
        generate_ntt_data(DATA_DIR, n_val, NTT_PRIME, file_id=i)
        
    print("\nLarge-scale data generation complete.") 