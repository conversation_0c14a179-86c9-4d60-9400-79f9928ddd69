import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import os

# --- Configuration ---
RESULTS_FILE = "results.csv"
OUTPUT_DIR = "charts"
N_VALUES_TO_PLOT = [4, 131072] # Plot for these specific problem sizes
ALGORITHM_ORDER = [
    'serial_r2_scalar',
    'mpi_r2_scalar',
    'mpi_r2_simd',
    'mpi_r4_scalar',
    'mpi_r4_simd'
]
PALETTE = sns.color_palette("viridis", len(ALGORITHM_ORDER))
DASH_STYLES = ["-", "--", "-.", ":", "-"]
DASH_STYLES_TUPLES = {
    '-': (None, None),
    '--': (3, 3),
    '-.': (4, 2, 1, 2),
    ':': (1, 2)
}
DASH_STYLES_MAPPED = [DASH_STYLES_TUPLES.get(s, (None,None)) for s in DASH_STYLES]

# --- Plotting Functions ---

def plot_time_vs_processes(df, n_val):
    """Plots execution time vs. number of processes for a given n."""
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax = plt.subplots(figsize=(12, 7))

    filtered_df = df[(df['n'] == n_val) & (df['algorithm'] != 'serial_r2_scalar')]

    sns.lineplot(
        data=filtered_df,
        x='processes',
        y='time_us',
        hue='algorithm',
        style='algorithm',
        hue_order=ALGORITHM_ORDER[1:],
        style_order=ALGORITHM_ORDER[1:],
        markers=True,
        dashes=True,
        palette=PALETTE[1:],
        lw=2.5,
        ax=ax
    )

    ax.set_title(f'Execution Time vs. Number of Processes (n={n_val})', fontsize=18, fontweight='bold')
    ax.set_xlabel('Number of Processes', fontsize=14)
    ax.set_ylabel('Execution Time (microseconds)', fontsize=14)
    ax.set_xscale('log', base=2)
    ax.set_yscale('log', base=10)
    ax.tick_params(axis='both', which='major', labelsize=12)
    ax.legend(title='Algorithm', fontsize=11)
    
    plt.tight_layout()
    plt.savefig(os.path.join(OUTPUT_DIR, f'time_vs_processes_n{n_val}.png'), dpi=300)
    plt.close()
    print(f"Generated time vs processes plot for n={n_val}")

def plot_speedup_vs_processes(df, n_val):
    """Plots speedup vs. number of processes for a given n."""
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax = plt.subplots(figsize=(12, 7))

    # Get serial time as baseline
    serial_time_series = df[(df['algorithm'] == 'serial_r2_scalar') & (df['n'] == n_val)]
    if serial_time_series.empty:
        print(f"Warning: No serial baseline found for n={n_val}. Cannot compute speedup.")
        # As a fallback, use the 1-process time for each algorithm as its own baseline
        df_n = df[df['n'] == n_val].copy()
        one_process_times = df_n[df_n['processes'] == 1].set_index('algorithm')['time_us']
        df_n['baseline_time'] = df_n['algorithm'].map(one_process_times)
    else:
        serial_time = serial_time_series['time_us'].iloc[0]
        # Replace 0.00 with a small number to avoid division by zero
        if serial_time == 0:
            serial_time = 0.01
        df_n = df[df['n'] == n_val].copy()
        df_n['baseline_time'] = serial_time

    df_n['speedup'] = df_n['baseline_time'] / df_n['time_us']

    # Filter out serial for plotting
    plot_df = df_n[df_n['algorithm'] != 'serial_r2_scalar']

    sns.lineplot(
        data=plot_df,
        x='processes',
        y='speedup',
        hue='algorithm',
        style='algorithm',
        hue_order=ALGORITHM_ORDER[1:],
        style_order=ALGORITHM_ORDER[1:],
        markers=True,
        dashes=True,
        palette=PALETTE[1:],
        lw=2.5,
        ax=ax
    )
    
    # Plot ideal speedup
    procs = sorted(plot_df['processes'].unique())
    ax.plot(procs, procs, 'k--', label='Ideal Speedup', lw=2)

    ax.set_title(f'Speedup vs. Number of Processes (n={n_val})', fontsize=18, fontweight='bold')
    ax.set_xlabel('Number of Processes', fontsize=14)
    ax.set_ylabel('Speedup Factor', fontsize=14)
    ax.set_xticks(procs)
    ax.tick_params(axis='both', which='major', labelsize=12)
    ax.legend(title='Algorithm', fontsize=11)
    
    plt.tight_layout()
    plt.savefig(os.path.join(OUTPUT_DIR, f'speedup_vs_processes_n{n_val}.png'), dpi=300)
    plt.close()
    print(f"Generated speedup vs processes plot for n={n_val}")

def plot_algorithm_comparison(df, processes_val):
    """Plots a bar chart comparing algorithms for a given number of processes."""
    plt.style.use('seaborn-v0_8-whitegrid')
    fig, ax = plt.subplots(figsize=(14, 8))

    filtered_df = df[((df['processes'] == processes_val) & (df['algorithm'] != 'serial_r2_scalar')) | (df['algorithm'] == 'serial_r2_scalar')]
    
    # Use the single process time for the serial algorithm regardless of the selected process count
    serial_rows = df[df['algorithm'] == 'serial_r2_scalar'].copy()
    if not serial_rows.empty:
        filtered_df = pd.concat([filtered_df[filtered_df['algorithm'] != 'serial_r2_scalar'], serial_rows], ignore_index=True)
    
    filtered_df = filtered_df.drop_duplicates(subset=['algorithm', 'n'])

    sns.barplot(
        data=filtered_df,
        x='n',
        y='time_us',
        hue='algorithm',
        hue_order=ALGORITHM_ORDER,
        palette=PALETTE,
        ax=ax
    )

    ax.set_title(f'Algorithm Performance Comparison (Processes = {processes_val})', fontsize=18, fontweight='bold')
    ax.set_xlabel('Problem Size (n)', fontsize=14)
    ax.set_ylabel('Execution Time (microseconds)', fontsize=14)
    ax.set_yscale('log')
    ax.tick_params(axis='x', which='major', labelsize=12, rotation=0)
    ax.tick_params(axis='y', which='major', labelsize=12)
    ax.legend(title='Algorithm', fontsize=11, loc='upper left')

    plt.tight_layout()
    plt.savefig(os.path.join(OUTPUT_DIR, f'algorithm_comparison_p{processes_val}.png'), dpi=300)
    plt.close()
    print(f"Generated algorithm comparison plot for {processes_val} processes")


# --- Main Logic ---
def main():
    print("--- Starting Visualization ---")
    
    # Check if results file exists
    if not os.path.exists(RESULTS_FILE):
        print(f"Error: Results file '{RESULTS_FILE}' not found.")
        print("Please run 'run_experiments.sh' first.")
        return

    # Create output directory
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # Load data
    df = pd.read_csv(RESULTS_FILE)
    print("Results data loaded successfully.")
    
    # Clean up redundant algorithm versions
    # We assume mpi_r2_scalar and mpi_r2_scalar_v2 are the same, prefer the first
    df = df[df.algorithm != 'mpi_r2_scalar_v2']
    # Same for Radix-4
    df = df[df.algorithm != 'mpi_r4_scalar_v2']


    # Generate plots
    for n in N_VALUES_TO_PLOT:
        if n in df['n'].unique():
            plot_time_vs_processes(df, n)
            plot_speedup_vs_processes(df, n)
        else:
            print(f"Warning: No data found for n={n}, skipping plots.")
    
    max_processes = df['processes'].max()
    plot_algorithm_comparison(df, max_processes)
    plot_algorithm_comparison(df, 1)


    print(f"--- Visualization Finished. Charts are saved in '{OUTPUT_DIR}' directory. ---")

if __name__ == "__main__":
    main() 