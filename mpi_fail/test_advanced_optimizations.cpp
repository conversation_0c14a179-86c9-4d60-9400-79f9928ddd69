/**
 * @file test_advanced_optimizations.cpp
 * @brief 深度NTT优化技术综合测试与性能对比
 * 
 * 测试内容：
 * 1. 自适应精度Barrett vs 传统Barrett
 * 2. 异步通信 vs 同步通信
 * 3. 混合并行 vs 单一并行模式
 * 4. 缓存优化数据布局 vs 标准布局
 * 5. 各种优化技术的组合效果
 */

#include "ntt_advanced_optimizations.h"
#include <chrono>
#include <iostream>
#include <iomanip>
#include <fstream>
#include <random>

using namespace ntt_advanced;

/**
 * @brief 传统Barrett约减器（用于对比）
 */
class TraditionalBarrett {
private:
    std::uint32_t mod_;
    std::uint64_t inv_;
    
public:
    explicit TraditionalBarrett(std::uint32_t mod) : mod_(mod) {
        inv_ = (~static_cast<std::uint64_t>(0)) / mod + 1;
    }
    
    std::uint32_t reduce(std::uint64_t x) const {
        std::uint64_t q = (static_cast<__uint128_t>(x) * inv_) >> 64;
        std::uint64_t r = x - q * mod_;
        return static_cast<std::uint32_t>(r >= mod_ ? r - mod_ : r);
    }
};

/**
 * @brief 测试数据生成器
 */
class TestDataGenerator {
private:
    std::mt19937 rng_;
    
public:
    TestDataGenerator() : rng_(std::random_device{}()) {}
    
    std::vector<std::uint32_t> generate_test_data(size_t size, std::uint32_t mod) {
        std::vector<std::uint32_t> data(size);
        std::uniform_int_distribution<std::uint32_t> dist(0, mod - 1);
        
        for (auto& val : data) {
            val = dist(rng_);
        }
        
        return data;
    }
};

/**
 * @brief 性能基准测试框架
 */
class BenchmarkFramework {
private:
    struct BenchmarkResult {
        std::string test_name;
        double execution_time_ms;
        double throughput_gops;
        size_t memory_usage_mb;
        double speedup_factor;
    };
    
    std::vector<BenchmarkResult> results_;
    
public:
    template<typename Func>
    void run_benchmark(const std::string& name, Func&& func, size_t ops_count) {
        auto start = std::chrono::high_resolution_clock::now();
        
        func();
        
        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        
        BenchmarkResult result;
        result.test_name = name;
        result.execution_time_ms = duration.count() / 1000.0;
        result.throughput_gops = (ops_count / 1e9) / (duration.count() / 1e6);
        result.memory_usage_mb = get_memory_usage();
        
        results_.push_back(result);
    }
    
    void print_results() const {
        std::cout << std::setfill('=') << std::setw(80) << "" << std::endl;
        std::cout << "                    深度NTT优化性能基准测试报告" << std::endl;
        std::cout << std::setfill('=') << std::setw(80) << "" << std::endl;
        std::cout << std::setfill(' ');
        
        std::cout << std::left << std::setw(30) << "测试项目"
                  << std::setw(15) << "执行时间(ms)"
                  << std::setw(15) << "吞吐量(GOps/s)"
                  << std::setw(15) << "内存使用(MB)"
                  << std::setw(10) << "加速比" << std::endl;
        
        std::cout << std::setfill('-') << std::setw(80) << "" << std::endl;
        std::cout << std::setfill(' ');
        
        double baseline_time = results_.empty() ? 1.0 : results_[0].execution_time_ms;
        
        for (const auto& result : results_) {
            double speedup = baseline_time / result.execution_time_ms;
            
            std::cout << std::left << std::setw(30) << result.test_name
                      << std::setw(15) << std::fixed << std::setprecision(3) << result.execution_time_ms
                      << std::setw(15) << std::fixed << std::setprecision(3) << result.throughput_gops
                      << std::setw(15) << result.memory_usage_mb
                      << std::setw(10) << std::fixed << std::setprecision(2) << speedup << "x"
                      << std::endl;
        }
        
        std::cout << std::setfill('=') << std::setw(80) << "" << std::endl;
    }
    
    void save_results_to_csv(const std::string& filename) const {
        std::ofstream file(filename);
        file << "Test Name,Execution Time (ms),Throughput (GOps/s),Memory Usage (MB),Speedup Factor\n";
        
        double baseline_time = results_.empty() ? 1.0 : results_[0].execution_time_ms;
        
        for (const auto& result : results_) {
            double speedup = baseline_time / result.execution_time_ms;
            file << result.test_name << ","
                 << result.execution_time_ms << ","
                 << result.throughput_gops << ","
                 << result.memory_usage_mb << ","
                 << speedup << "\n";
        }
    }
    
private:
    size_t get_memory_usage() const {
        std::ifstream status("/proc/self/status");
        std::string line;
        while (std::getline(status, line)) {
            if (line.substr(0, 6) == "VmRSS:") {
                size_t kb = std::stoul(line.substr(7));
                return kb / 1024;
            }
        }
        return 0;
    }
};

/**
 * @brief Barrett约减器性能对比测试
 */
void test_barrett_comparison(BenchmarkFramework& benchmark) {
    const size_t test_size = 1000000;
    const std::uint32_t mod = 998244353;
    
    TestDataGenerator generator;
    auto test_data = generator.generate_test_data(test_size, UINT64_MAX);
    
    std::vector<std::uint64_t> input_data(test_size);
    for (size_t i = 0; i < test_size; ++i) {
        input_data[i] = static_cast<std::uint64_t>(test_data[i]) * test_data[(i + 1) % test_size];
    }
    
    benchmark.run_benchmark("传统Barrett约减", [&]() {
        TraditionalBarrett barrett(mod);
        std::vector<std::uint32_t> output(test_size);
        for (size_t i = 0; i < test_size; ++i) {
            output[i] = barrett.reduce(input_data[i]);
        }
    }, test_size);
    
    benchmark.run_benchmark("自适应精度Barrett", [&]() {
        AdaptivePrecisionBarrett<std::uint32_t> barrett(mod);
        std::vector<std::uint32_t> output(test_size);
        barrett.batch_reduce(input_data.data(), output.data(), test_size);
    }, test_size);
}

/**
 * @brief 通信模式性能对比测试
 */
void test_communication_patterns(BenchmarkFramework& benchmark, MPI_Comm comm) {
    int rank, size;
    MPI_Comm_rank(comm, &rank);
    MPI_Comm_size(comm, &size);
    
    if (size < 2) {
        std::cout << "通信测试需要至少2个进程\n";
        return;
    }
    
    const size_t data_size = 100000;
    TestDataGenerator generator;
    auto test_data = generator.generate_test_data(data_size, 1000000);
    
    benchmark.run_benchmark("同步All-to-All通信", [&]() {
        std::vector<std::uint32_t> send_buf = test_data;
        std::vector<std::uint32_t> recv_buf(data_size);
        
        MPI_Alltoall(send_buf.data(), static_cast<int>(data_size / size), MPI_UINT32_T,
                     recv_buf.data(), static_cast<int>(data_size / size), MPI_UINT32_T, comm);
    }, data_size);
    
    benchmark.run_benchmark("异步All-to-All通信", [&]() {
        AsyncCommManager async_comm(comm);
        
        std::vector<std::vector<std::uint32_t>> send_data(size);
        for (int i = 0; i < size; ++i) {
            send_data[i].assign(test_data.begin() + i * (data_size / size),
                               test_data.begin() + (i + 1) * (data_size / size));
        }
        
        std::vector<std::vector<std::uint32_t>> recv_data;
        async_comm.start_nonblocking_alltoall(send_data, recv_data);
        async_comm.wait_all();
    }, data_size);
}

/**
 * @brief 并行模式性能对比测试
 */
void test_parallel_modes(BenchmarkFramework& benchmark, MPI_Comm comm) {
    const size_t data_size = 1048576;
    const std::uint32_t mod = 998244353;
    
    TestDataGenerator generator;
    auto test_data = generator.generate_test_data(data_size, mod);
    
    benchmark.run_benchmark("纯MPI并行NTT", [&]() {
        std::vector<std::uint32_t> data_copy = test_data;
        
    }, data_size);
    
    benchmark.run_benchmark("MPI+OpenMP混合并行", [&]() {
        HybridParallelNTT hybrid_ntt(comm, 4);
        std::vector<std::uint32_t> data_copy = test_data;
        hybrid_ntt.adaptive_parallel_ntt(data_copy.data(), data_size, false, mod);
    }, data_size);
    
    benchmark.run_benchmark("三级混合并行NTT", [&]() {
        HybridParallelNTT hybrid_ntt(comm, 4);
        std::vector<std::uint32_t> data_copy = test_data;
        hybrid_ntt.three_level_parallel_ntt(data_copy.data(), data_size, false, mod);
    }, data_size);
}

/**
 * @brief 缓存优化效果测试
 */
void test_cache_optimizations(BenchmarkFramework& benchmark) {
    const size_t matrix_size = 1024;
    
    benchmark.run_benchmark("标准矩阵转置", [&]() {
        std::vector<std::uint32_t> matrix(matrix_size * matrix_size);
        
        for (size_t i = 0; i < matrix_size; ++i) {
            matrix[i * matrix_size + i] = static_cast<std::uint32_t>(i);
        }
        
        for (size_t i = 0; i < matrix_size; ++i) {
            for (size_t j = i + 1; j < matrix_size; ++j) {
                std::swap(matrix[i * matrix_size + j], matrix[j * matrix_size + i]);
            }
        }
    }, matrix_size * matrix_size);
    
    benchmark.run_benchmark("分块矩阵转置", [&]() {
        BlockedMatrixLayout matrix(matrix_size, matrix_size, 64);
        
        for (size_t i = 0; i < matrix_size; ++i) {
            matrix(i, i) = static_cast<std::uint32_t>(i);
        }
        
        matrix.blocked_transpose();
    }, matrix_size * matrix_size);
}

/**
 * @brief 扩展性分析测试
 */
void test_scalability_analysis(BenchmarkFramework& benchmark, MPI_Comm comm) {
    int rank, size;
    MPI_Comm_rank(comm, &rank);
    MPI_Comm_size(comm, &size);
    
    const std::vector<size_t> problem_sizes = {4096, 16384, 65536, 262144};
    const std::uint32_t mod = 998244353;
    
    TestDataGenerator generator;
    
    for (size_t problem_size : problem_sizes) {
        auto test_data = generator.generate_test_data(problem_size, mod);
        
        std::string test_name = "扩展性测试-N=" + std::to_string(problem_size) + "-P=" + std::to_string(size);
        
        benchmark.run_benchmark(test_name, [&]() {
            HybridParallelNTT hybrid_ntt(comm, 2);
            std::vector<std::uint32_t> data_copy = test_data;
            hybrid_ntt.adaptive_parallel_ntt(data_copy.data(), problem_size, false, mod);
        }, problem_size);
    }
}

/**
 * @brief 主测试函数
 */
int main(int argc, char** argv) {
    MPI_Init(&argc, &argv);
    
    int rank, size;
    MPI_Comm_rank(MPI_COMM_WORLD, &rank);
    MPI_Comm_size(MPI_COMM_WORLD, &size);
    
    if (rank == 0) {
        std::cout << "深度NTT优化技术综合测试启动\n";
        std::cout << "MPI进程数: " << size << std::endl;
        std::cout << "OpenMP线程数: " << omp_get_max_threads() << std::endl;
        std::cout << "========================================\n";
    }
    
    BenchmarkFramework benchmark;
    
    if (rank == 0) {
        std::cout << "\n1. Barrett约减器性能对比测试...\n";
        test_barrett_comparison(benchmark);
        
        std::cout << "\n4. 缓存优化效果测试...\n";
        test_cache_optimizations(benchmark);
    }
    
    MPI_Barrier(MPI_COMM_WORLD);
    
    if (rank == 0) std::cout << "\n2. 通信模式性能对比测试...\n";
    test_communication_patterns(benchmark, MPI_COMM_WORLD);
    
    MPI_Barrier(MPI_COMM_WORLD);
    
    if (rank == 0) std::cout << "\n3. 并行模式性能对比测试...\n";
    test_parallel_modes(benchmark, MPI_COMM_WORLD);
    
    MPI_Barrier(MPI_COMM_WORLD);
    
    if (rank == 0) std::cout << "\n5. 扩展性分析测试...\n";
    test_scalability_analysis(benchmark, MPI_COMM_WORLD);
    
    if (rank == 0) {
        std::cout << "\n========================================\n";
        std::cout << "所有测试完成，生成性能报告...\n";
        benchmark.print_results();
        benchmark.save_results_to_csv("advanced_ntt_benchmark_results.csv");
        
        std::cout << "\n深度优化建议：\n";
        std::cout << "1. 对于小模数(<2^31)，使用自适应精度Barrett约减器\n";
        std::cout << "2. 对于大规模并行(P>=8)，启用异步通信重叠\n";
        std::cout << "3. 对于NUMA系统，使用混合并行+线程绑定\n";
        std::cout << "4. 对于缓存敏感应用，使用分块数据布局\n";
        std::cout << "5. 根据问题规模自适应选择并行策略\n";
    }
    
    MPI_Finalize();
    return 0;
} 