/**
 * @file async_comm_manager.cpp
 * @brief 异步通信管理器实现
 * 
 * 核心优化策略：
 * 1. 非阻塞通信与计算重叠
 * 2. 双缓冲机制减少等待时间
 * 3. 智能调度算法优化通信模式
 * 4. 带宽感知的数据分块传输
 */

#include "ntt_advanced_optimizations.h"
#include <algorithm>
#include <thread>
#include <atomic>

namespace ntt_advanced {

AsyncCommManager::AsyncCommManager(MPI_Comm comm) : comm_(comm) {
    int rank, size;
    MPI_Comm_rank(comm_, &rank);
    MPI_Comm_size(comm_, &size);
    
    requests_.reserve(size * 2);
    send_buffers_.resize(size);
    recv_buffers_.resize(size);
}

std::future<void> AsyncCommManager::start_async_exchange(
    const std::vector<std::uint32_t>& send_data,
    int dest_rank,
    int tag) {
    
    return std::async(std::launch::async, [this, send_data, dest_rank, tag]() {
        const size_t chunk_size = 64 * 1024;
        const size_t num_chunks = (send_data.size() + chunk_size - 1) / chunk_size;
        
        std::vector<MPI_Request> chunk_requests(num_chunks * 2);
        std::vector<std::vector<std::uint32_t>> chunk_buffers(num_chunks);
        
        for (size_t i = 0; i < num_chunks; ++i) {
            size_t start_idx = i * chunk_size;
            size_t end_idx = std::min(start_idx + chunk_size, send_data.size());
            
            chunk_buffers[i].assign(
                send_data.begin() + start_idx,
                send_data.begin() + end_idx
            );
            
            MPI_Isend(
                chunk_buffers[i].data(),
                static_cast<int>(chunk_buffers[i].size()),
                MPI_UINT32_T,
                dest_rank,
                tag + static_cast<int>(i),
                comm_,
                &chunk_requests[i * 2]
            );
            
            MPI_Irecv(
                chunk_buffers[i].data(),
                static_cast<int>(chunk_buffers[i].size()),
                MPI_UINT32_T,
                dest_rank,
                tag + static_cast<int>(i) + 1000,
                comm_,
                &chunk_requests[i * 2 + 1]
            );
        }
        
        MPI_Waitall(
            static_cast<int>(chunk_requests.size()),
            chunk_requests.data(),
            MPI_STATUSES_IGNORE
        );
    });
}

void AsyncCommManager::start_nonblocking_alltoall(
    const std::vector<std::vector<std::uint32_t>>& send_data,
    std::vector<std::vector<std::uint32_t>>& recv_data) {
    
    int rank, size;
    MPI_Comm_rank(comm_, &rank);
    MPI_Comm_size(comm_, &size);
    
    recv_data.resize(size);
    requests_.clear();
    
    constexpr size_t OPTIMAL_CHUNK_SIZE = 32768;
    
    for (int dest = 0; dest < size; ++dest) {
        if (dest == rank) continue;
        
        const auto& data_to_send = send_data[dest];
        if (data_to_send.empty()) continue;
        
        size_t num_chunks = (data_to_send.size() + OPTIMAL_CHUNK_SIZE - 1) / OPTIMAL_CHUNK_SIZE;
        
        send_buffers_[dest].resize(num_chunks);
        recv_buffers_[dest].resize(num_chunks);
        
        for (size_t chunk = 0; chunk < num_chunks; ++chunk) {
            size_t start_pos = chunk * OPTIMAL_CHUNK_SIZE;
            size_t chunk_size = std::min(OPTIMAL_CHUNK_SIZE, data_to_send.size() - start_pos);
            
            send_buffers_[dest][chunk].assign(
                data_to_send.begin() + start_pos,
                data_to_send.begin() + start_pos + chunk_size
            );
            
            recv_buffers_[dest][chunk].resize(chunk_size);
            
            MPI_Request send_req, recv_req;
            
            MPI_Isend(
                send_buffers_[dest][chunk].data(),
                static_cast<int>(chunk_size),
                MPI_UINT32_T,
                dest,
                static_cast<int>(chunk),
                comm_,
                &send_req
            );
            
            MPI_Irecv(
                recv_buffers_[dest][chunk].data(),
                static_cast<int>(chunk_size),
                MPI_UINT32_T,
                dest,
                static_cast<int>(chunk),
                comm_,
                &recv_req
            );
            
            requests_.push_back(send_req);
            requests_.push_back(recv_req);
        }
    }
}

void AsyncCommManager::wait_all() {
    if (requests_.empty()) return;
    
    constexpr int BATCH_SIZE = 64;
    const int total_requests = static_cast<int>(requests_.size());
    
    for (int start = 0; start < total_requests; start += BATCH_SIZE) {
        int batch_end = std::min(start + BATCH_SIZE, total_requests);
        int batch_size = batch_end - start;
        
        std::vector<int> completed_indices(batch_size);
        std::vector<MPI_Status> statuses(batch_size);
        
        int completed_count = 0;
        while (completed_count < batch_size) {
            int newly_completed;
            MPI_Waitsome(
                batch_size - completed_count,
                &requests_[start + completed_count],
                &newly_completed,
                completed_indices.data(),
                statuses.data()
            );
            
            if (newly_completed != MPI_UNDEFINED) {
                completed_count += newly_completed;
            }
            
            std::this_thread::yield();
        }
    }
    
    requests_.clear();
    
    for (auto& buffers : send_buffers_) {
        buffers.clear();
    }
    for (auto& buffers : recv_buffers_) {
        buffers.clear();
    }
}

class OptimizedAllToAllScheduler {
private:
    MPI_Comm comm_;
    int rank_, size_;
    std::vector<std::vector<int>> communication_graph_;
    
public:
    explicit OptimizedAllToAllScheduler(MPI_Comm comm) : comm_(comm) {
        MPI_Comm_rank(comm_, &rank_);
        MPI_Comm_size(comm_, &size_);
        build_communication_graph();
    }
    
private:
    void build_communication_graph() {
        communication_graph_.resize(size_);
        for (int phase = 0; phase < size_ - 1; ++phase) {
            for (int i = 0; i < size_; ++i) {
                if (i == rank_) continue;
                
                int partner = (rank_ + i) % size_;
                if (partner != rank_) {
                    communication_graph_[phase].push_back(partner);
                }
            }
        }
    }
    
public:
    void execute_scheduled_alltoall(
        const std::vector<std::vector<std::uint32_t>>& send_data,
        std::vector<std::vector<std::uint32_t>>& recv_data) {
        
        recv_data.resize(size_);
        
        for (int phase = 0; phase < size_ - 1; ++phase) {
            std::vector<MPI_Request> phase_requests;
            
            for (int partner : communication_graph_[phase]) {
                if (!send_data[partner].empty()) {
                    recv_data[partner].resize(send_data[partner].size());
                    
                    MPI_Request send_req, recv_req;
                    
                    MPI_Isend(
                        const_cast<std::uint32_t*>(send_data[partner].data()),
                        static_cast<int>(send_data[partner].size()),
                        MPI_UINT32_T,
                        partner,
                        phase,
                        comm_,
                        &send_req
                    );
                    
                    MPI_Irecv(
                        recv_data[partner].data(),
                        static_cast<int>(recv_data[partner].size()),
                        MPI_UINT32_T,
                        partner,
                        phase,
                        comm_,
                        &recv_req
                    );
                    
                    phase_requests.push_back(send_req);
                    phase_requests.push_back(recv_req);
                }
            }
            
            MPI_Waitall(
                static_cast<int>(phase_requests.size()),
                phase_requests.data(),
                MPI_STATUSES_IGNORE
            );
        }
    }
};

class BandwidthAwareComm {
private:
    MPI_Comm comm_;
    double measured_bandwidth_;
    size_t optimal_chunk_size_;
    
public:
    explicit BandwidthAwareComm(MPI_Comm comm) : comm_(comm) {
        measure_bandwidth();
        calculate_optimal_chunk_size();
    }
    
private:
    void measure_bandwidth() {
        const size_t test_size = 1024 * 1024;
        std::vector<std::uint32_t> test_data(test_size);
        
        int rank, size;
        MPI_Comm_rank(comm_, &rank);
        MPI_Comm_size(comm_, &size);
        
        if (size < 2) {
            measured_bandwidth_ = 1e9;
            return;
        }
        
        MPI_Barrier(comm_);
        auto start = std::chrono::high_resolution_clock::now();
        
        if (rank == 0) {
            MPI_Send(test_data.data(), static_cast<int>(test_size), MPI_UINT32_T, 1, 0, comm_);
        } else if (rank == 1) {
            MPI_Recv(test_data.data(), static_cast<int>(test_size), MPI_UINT32_T, 0, 0, comm_, MPI_STATUS_IGNORE);
        }
        
        MPI_Barrier(comm_);
        auto end = std::chrono::high_resolution_clock::now();
        
        auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start);
        double bytes_transferred = test_size * sizeof(std::uint32_t);
        measured_bandwidth_ = bytes_transferred / (duration.count() * 1e-6);
    }
    
    void calculate_optimal_chunk_size() {
        double latency_estimate = 10e-6;
        optimal_chunk_size_ = static_cast<size_t>(measured_bandwidth_ * latency_estimate / sizeof(std::uint32_t));
        
        optimal_chunk_size_ = std::max(optimal_chunk_size_, size_t{4096});
        optimal_chunk_size_ = std::min(optimal_chunk_size_, size_t{1024 * 1024});
        
        optimal_chunk_size_ = (optimal_chunk_size_ + 63) & ~63ULL;
    }
    
public:
    size_t get_optimal_chunk_size() const { return optimal_chunk_size_; }
    double get_bandwidth() const { return measured_bandwidth_; }
};

} // namespace ntt_advanced 