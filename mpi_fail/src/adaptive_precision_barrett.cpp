/**
 * @file adaptive_precision_barrett.cpp
 * @brief 自适应精度Barrett约减器实现
 * 
 * 关键优化点：
 * 1. 模数大小感知的计算路径选择
 * 2. SIMD批量处理优化
 * 3. 编译时模板特化
 * 4. 分支预测友好的设计
 */

#include "ntt_advanced_optimizations.h"
#include <immintrin.h>
#include <arm_neon.h>
#include <cstring>

namespace ntt_advanced {

template<typename T>
AdaptivePrecisionBarrett<T>::AdaptivePrecisionBarrett(T mod) : mod_(mod) {
    if (mod == 0) {
        inv_ = 0;
        use_fast_path_ = false;
        return;
    }
    
    inv_ = static_cast<T>((~static_cast<std::uint64_t>(0)) / mod + 1);
    
    use_fast_path_ = (mod <= (1ULL << 31)) && (mod > 1);
}

template<typename T>
T AdaptivePrecisionBarrett<T>::reduce(std::uint64_t x) const {
    if (use_fast_path_) {
        std::uint64_t q = (static_cast<__uint128_t>(x) * inv_) >> 64;
        std::uint64_t r = x - q * mod_;
        return static_cast<T>(r >= mod_ ? r - mod_ : r);
    } else {
        return static_cast<T>(x % mod_);
    }
}

template<typename T>
void AdaptivePrecisionBarrett<T>::batch_reduce(
    const std::uint64_t* input, 
    T* output, 
    size_t count) const {
    
    if (!use_fast_path_) {
        for (size_t i = 0; i < count; ++i) {
            output[i] = static_cast<T>(input[i] % mod_);
        }
        return;
    }

#ifdef __AVX2__
    if (count >= 4 && sizeof(T) == 4) {
        const size_t simd_count = count & ~3ULL;
        
        const __m256i mod_vec = _mm256_set1_epi64x(mod_);
        const __m256i inv_vec = _mm256_set1_epi64x(inv_);
        
        for (size_t i = 0; i < simd_count; i += 4) {
            __m256i x_lo = _mm256_loadu_si256(reinterpret_cast<const __m256i*>(&input[i]));
            __m256i x_hi = _mm256_loadu_si256(reinterpret_cast<const __m256i*>(&input[i + 2]));
            
            __m256i q_lo = _mm256_mul_epu32(x_lo, inv_vec);
            __m256i q_hi = _mm256_mul_epu32(x_hi, inv_vec);
            
            q_lo = _mm256_srli_epi64(q_lo, 32);
            q_hi = _mm256_srli_epi64(q_hi, 32);
            
            __m256i prod_lo = _mm256_mul_epu32(q_lo, mod_vec);
            __m256i prod_hi = _mm256_mul_epu32(q_hi, mod_vec);
            
            __m256i r_lo = _mm256_sub_epi64(x_lo, prod_lo);
            __m256i r_hi = _mm256_sub_epi64(x_hi, prod_hi);
            
            __m256i cmp_lo = _mm256_cmpgt_epi64(r_lo, mod_vec);
            __m256i cmp_hi = _mm256_cmpgt_epi64(r_hi, mod_vec);
            
            r_lo = _mm256_sub_epi64(r_lo, _mm256_and_si256(cmp_lo, mod_vec));
            r_hi = _mm256_sub_epi64(r_hi, _mm256_and_si256(cmp_hi, mod_vec));
            
            __m128i result_lo = _mm256_cvtepi64_epi32(r_lo);
            __m128i result_hi = _mm256_cvtepi64_epi32(r_hi);
            
            _mm_storeu_si128(reinterpret_cast<__m128i*>(&output[i]), result_lo);
            _mm_storeu_si128(reinterpret_cast<__m128i*>(&output[i + 2]), result_hi);
        }
        
        for (size_t i = simd_count; i < count; ++i) {
            output[i] = reduce(input[i]);
        }
        return;
    }
#endif

#ifdef __ARM_NEON
    if (count >= 2 && sizeof(T) == 4) {
        const size_t neon_count = count & ~1ULL;
        
        const uint64x2_t mod_vec = vdupq_n_u64(mod_);
        const uint64x2_t inv_vec = vdupq_n_u64(inv_);
        
        for (size_t i = 0; i < neon_count; i += 2) {
            uint64x2_t x_vec = vld1q_u64(&input[i]);
            
            uint64x2_t q_vec = vmull_high_u32(
                vreinterpretq_u32_u64(x_vec), 
                vreinterpretq_u32_u64(inv_vec)
            );
            
            uint64x2_t prod_vec = vmulq_u64(q_vec, mod_vec);
            uint64x2_t r_vec = vsubq_u64(x_vec, prod_vec);
            
            uint64x2_t cmp_vec = vcgtq_u64(r_vec, mod_vec);
            r_vec = vsubq_u64(r_vec, vandq_u64(cmp_vec, mod_vec));
            
            uint32x2_t result = vmovn_u64(r_vec);
            vst1_u32(reinterpret_cast<std::uint32_t*>(&output[i]), result);
        }
        
        for (size_t i = neon_count; i < count; ++i) {
            output[i] = reduce(input[i]);
        }
        return;
    }
#endif

    for (size_t i = 0; i < count; ++i) {
        output[i] = reduce(input[i]);
    }
}

template class AdaptivePrecisionBarrett<std::uint32_t>;
template class AdaptivePrecisionBarrett<std::uint64_t>;

} // namespace ntt_advanced 