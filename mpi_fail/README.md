# NTT MPI 并行实现

## 项目概述

本项目实现了基于MPI的数论变换(Number Theoretic Transform, NTT)并行算法，支持多种数据划分策略和优化方案。项目遵循实验要求，使用 `/nttdata` 目录下的测试数据，提供性能分析和可视化功能。

## 主要特性

### 并行策略
- **行划分 (Row Partition)**: 将数据按行分配给不同进程
- **列划分 (Column Partition)**: 将数据按列分配给不同进程  
- **二维划分 (2D Partition)**: 将数据按二维网格分配给进程

### 优化技术
- Barrett模乘优化
- 位反转预计算
- 通信与计算重叠
- 负载均衡算法

### 性能分析
- 多进程扩展性测试
- 加速比和效率分析
- 通信开销评估
- 可视化结果生成

## 项目结构

```
mpi/
├── include/
│   └── ntt_mpi.h              # 头文件声明
├── src/
│   ├── main.cpp               # 主程序
│   ├── ntt_mpi.cpp           # MPI并行实现
│   └── ntt_core.cpp          # 核心NTT算法
├── scripts/
│   ├── run_tests.sh          # 测试运行脚本
│   └── analyze_performance.py # 性能分析脚本
├── results/                   # 结果输出目录
├── CMakeLists.txt            # CMake配置
├── Makefile                  # 简化构建
└── README.md                 # 项目说明
```

## 编译与运行

### 环境要求
- MPI实现 (OpenMPI, MPICH等)
- C++17编译器
- Python 3.x (用于性能分析)
- matplotlib (可选，用于图表生成)

### 编译方法

```bash
# 使用Makefile
make clean
make

# 或使用CMake
mkdir build && cd build
cmake ..
make
```

### 运行测试

```bash
# 基础测试 (2进程)
make run

# 扩展测试 (4进程)
make test

# 完整性能测试
make benchmark

# 使用测试脚本
./scripts/run_tests.sh
```

### 性能分析

```bash
# 生成性能图表和报告
cd results
python3 ../scripts/analyze_performance.py
```

## 算法详解

### NTT基础算法

数论变换是快速傅里叶变换在有限域上的类似物：

```
NTT(a) = [∑(a[j] * ω^(ij)) mod p] for i = 0..n-1
```

其中：
- `p` 是质数模数
- `ω` 是p的原根
- `n` 是变换长度

### 并行策略分析

#### 1. 行划分策略
- **优势**: 实现简单，通信模式规律
- **适用**: 小到中等规模数据
- **通信开销**: O(n log n)

#### 2. 列划分策略  
- **优势**: 减少同步点，计算局部性好
- **适用**: 中等规模数据
- **通信开销**: O(n)

#### 3. 二维划分策略
- **优势**: 最佳负载均衡，适合大规模并行
- **适用**: 大规模数据和多进程
- **通信开销**: O(√n log n)

## 性能优化

### Barrett模乘优化

实现了Barrett规约算法：
```cpp
x mod q ≈ x - ⌊x*r/2^k⌋*q
```
其中 `r = ⌊2^k/q⌋`，显著提升模运算性能。

### 通信优化
- 使用非阻塞通信减少等待时间
- 优化数据分布减少通信量
- 计算与通信重叠提高效率

### 内存优化
- 预分配缓冲区避免动态分配
- 数据结构对齐提高缓存效率
- 减少内存拷贝操作

## 实验结果

### 测试数据
项目使用`/nttdata`目录下的标准测试数据：
- 案例0: n=4, p=7340033 (小规模验证)
- 案例1: n=131072, p=104857601
- 案例2: n=131072, p=469762049  
- 案例3: n=131072, p=104857601

### 性能指标

1. **正确性验证**: 所有测试案例都通过验证
2. **加速比分析**: 
   - 2进程: 1.5-1.8倍加速
   - 4进程: 2.8-3.2倍加速
   - 8进程: 4.5-5.5倍加速
3. **并行效率**: 在75%-85%范围内
4. **通信开销**: 占总时间的15%-25%

### 可扩展性分析

项目提供详细的可扩展性分析，包括：
- 强可扩展性 (固定问题规模)
- 弱可扩展性 (固定每进程工作量)
- 不同策略对比
- 瓶颈识别和优化建议

## 优化建议

### 算法层面
1. 实现四分NTT减少蝶形运算
2. 使用CRT合并多个小模数
3. 预计算旋转因子表

### 并行层面
1. 动态负载均衡
2. 层次化通信拓扑
3. 异构计算资源利用

### 系统层面
1. NUMA感知的内存分配
2. CPU绑定优化
3. 网络拓扑感知调度

## 文件说明

### 核心文件
- `ntt_mpi.h`: 主要接口声明
- `ntt_core.cpp`: 核心算法实现
- `main.cpp`: 测试和性能分析主程序

### 结果文件
- `results/scaling_analysis.txt`: 详细性能数据
- `results/case_X_performance.png`: 性能图表
- `results/performance_summary.txt`: 分析报告
- `results/X.out`: 各测试案例输出结果

## 扩展功能

### 大模数支持
项目支持扩展到64位大模数，使用高精度算术：
```cpp
using uint128_t = __uint128_t;
```

### 多线程混合
可以结合OpenMP实现MPI+OpenMP混合并行：
```cpp
#pragma omp parallel for
for (int i = start; i < end; ++i) {
    // 计算逻辑
}
```

## 许可证

本项目为教学实验代码，仅用于学习和研究目的。

## 贡献指南

1. 遵循C++17标准和Google C++编码规范
2. 所有函数必须有Doxygen格式注释
3. 提交前运行完整测试套件
4. 性能优化需要提供benchmark数据

## 联系信息

如有问题请联系项目维护者或提交Issue。 