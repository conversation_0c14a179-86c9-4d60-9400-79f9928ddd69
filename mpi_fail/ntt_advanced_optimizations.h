/**
 * @file ntt_advanced_optimizations.h
 * @brief 高级NTT MPI优化策略集合
 * <AUTHOR> Assistant
 * 
 * 本文件包含超越传统优化的深度NTT优化技术：
 * 1. 混合精度计算优化
 * 2. 缓存友好数据布局
 * 3. 预取与流水线优化  
 * 4. 通信计算重叠
 * 5. 动态负载均衡
 * 6. 多级并行混合
 * 7. 内存带宽优化
 * 8. 分层NTT算法
 */

#ifndef NTT_ADVANCED_OPTIMIZATIONS_H
#define NTT_ADVANCED_OPTIMIZATIONS_H

#include <mpi.h>
#include <vector>
#include <memory>
#include <future>
#include <chrono>
#include <cstdint>

namespace ntt_advanced {

/* ================= 混合精度计算优化 ================= */

/**
 * @brief 多精度Barrett约减器
 * 根据模数大小自适应选择最优精度
 */
template<typename T>
class AdaptivePrecisionBarrett {
private:
    T mod_;
    T inv_;
    bool use_fast_path_;
    
public:
    explicit AdaptivePrecisionBarrett(T mod);
    
    /**
     * @brief 自适应精度约减
     * 小模数使用快速路径，大模数使用高精度路径
     */
    T reduce(std::uint64_t x) const;
    
    /**
     * @brief 批量约减优化
     * 利用SIMD并行处理多个约减操作
     */
    void batch_reduce(const std::uint64_t* input, T* output, size_t count) const;
};

/* ================= 缓存友好数据布局 ================= */

/**
 * @brief 分块矩阵存储管理器
 * 优化数据局部性，减少缓存miss
 */
class BlockedMatrixLayout {
private:
    size_t block_size_;
    size_t rows_, cols_;
    std::vector<std::uint32_t> data_;
    
public:
    BlockedMatrixLayout(size_t rows, size_t cols, size_t block_size = 64);
    
    /**
     * @brief 获取元素引用（缓存友好访问）
     */
    std::uint32_t& operator()(size_t i, size_t j);
    const std::uint32_t& operator()(size_t i, size_t j) const;
    
    /**
     * @brief 分块转置操作
     * 最小化跨缓存行访问
     */
    void blocked_transpose();
    
    /**
     * @brief 获取连续内存块用于MPI传输
     */
    std::vector<std::uint32_t> get_block_for_comm(size_t block_row, size_t block_col) const;
};

/* ================= 预取与流水线优化 ================= */

/**
 * @brief 预取感知的NTT蝶形运算器
 */
class PrefetchAwareButterfly {
private:
    static constexpr size_t PREFETCH_DISTANCE = 8;
    
public:
    /**
     * @brief 带预取的蝶形运算
     * 提前预取下一次迭代需要的数据
     */
    static void butterfly_with_prefetch(
        std::uint32_t* data, 
        size_t stride, 
        size_t count,
        std::uint32_t twiddle,
        std::uint32_t mod
    );
    
    /**
     * @brief 流水线式批量蝶形运算
     * 重叠内存访问与计算
     */
    static void pipelined_butterfly_batch(
        std::uint32_t* data,
        const std::vector<size_t>& strides,
        const std::vector<std::uint32_t>& twiddles,
        std::uint32_t mod
    );
};

/* ================= 通信计算重叠 ================= */

/**
 * @brief 异步通信管理器
 * 实现计算与通信的完全重叠
 */
class AsyncCommManager {
private:
    MPI_Comm comm_;
    std::vector<MPI_Request> requests_;
    std::vector<std::vector<std::uint32_t>> send_buffers_;
    std::vector<std::vector<std::uint32_t>> recv_buffers_;
    
public:
    explicit AsyncCommManager(MPI_Comm comm);
    
    /**
     * @brief 启动异步数据交换
     * 返回future对象，可在计算时等待完成
     */
    std::future<void> start_async_exchange(
        const std::vector<std::uint32_t>& send_data,
        int dest_rank,
        int tag
    );
    
    /**
     * @brief 非阻塞All-to-All通信
     * 与本地计算重叠执行
     */
    void start_nonblocking_alltoall(
        const std::vector<std::vector<std::uint32_t>>& send_data,
        std::vector<std::vector<std::uint32_t>>& recv_data
    );
    
    /**
     * @brief 等待所有异步操作完成
     */
    void wait_all();
};

/* ================= 动态负载均衡 ================= */

/**
 * @brief 工作窃取式负载均衡器
 */
class WorkStealingBalancer {
private:
    struct WorkItem {
        size_t start_idx;
        size_t end_idx;
        int complexity_level;
    };
    
    std::vector<std::vector<WorkItem>> work_queues_;
    MPI_Comm comm_;
    int rank_, size_;
    
public:
    WorkStealingBalancer(MPI_Comm comm);
    
    /**
     * @brief 动态分配工作任务
     * 根据处理器性能和当前负载调整
     */
    std::vector<WorkItem> get_balanced_workload(
        size_t total_work,
        const std::vector<double>& processor_speeds
    );
    
    /**
     * @brief 运行时负载重平衡
     * 检测负载不均并重新分配任务
     */
    void rebalance_if_needed();
};

/* ================= 多级并行混合 ================= */

/**
 * @brief MPI+OpenMP混合并行NTT
 */
class HybridParallelNTT {
private:
    MPI_Comm mpi_comm_;
    int num_threads_;
    
public:
    HybridParallelNTT(MPI_Comm comm, int threads);
    
    /**
     * @brief 三级并行NTT
     * MPI进程间 + OpenMP线程间 + SIMD指令级
     */
    void three_level_parallel_ntt(
        std::uint32_t* data,
        size_t length,
        bool inverse,
        std::uint32_t mod
    );
    
    /**
     * @brief 自适应并行策略选择
     * 根据问题规模选择最优并行方案
     */
    void adaptive_parallel_ntt(
        std::uint32_t* data,
        size_t length,
        bool inverse,
        std::uint32_t mod
    );
};

/* ================= 内存带宽优化 ================= */

/**
 * @brief 内存带宽感知的数据访问模式优化器
 */
class MemoryBandwidthOptimizer {
private:
    size_t cache_line_size_;
    size_t l1_cache_size_;
    size_t l2_cache_size_;
    
public:
    MemoryBandwidthOptimizer();
    
    /**
     * @brief 优化数据访问模式
     * 最大化内存带宽利用率
     */
    void optimize_access_pattern(
        std::uint32_t* data,
        size_t length,
        const std::vector<size_t>& access_indices
    );
    
    /**
     * @brief 流式内存访问
     * 连续的、预测性的内存访问模式
     */
    void streaming_memory_access(
        const std::uint32_t* input,
        std::uint32_t* output,
        size_t length,
        std::function<std::uint32_t(std::uint32_t)> transform
    );
};

/* ================= 分层NTT算法 ================= */

/**
 * @brief 层次化NTT计算框架
 */
class HierarchicalNTT {
private:
    struct NTTLevel {
        size_t level_size;
        std::vector<std::uint32_t> twiddle_factors;
        bool use_parallel;
    };
    
    std::vector<NTTLevel> levels_;
    
public:
    /**
     * @brief 构建NTT计算层次
     * 根据数据规模和硬件特性分层
     */
    void build_hierarchy(size_t total_size, MPI_Comm comm);
    
    /**
     * @brief 分层执行NTT
     * 每层采用最优算法策略
     */
    void execute_hierarchical_ntt(
        std::uint32_t* data,
        size_t length,
        bool inverse,
        std::uint32_t mod
    );
    
    /**
     * @brief 自底向上的NTT合并
     * 高效合并各层计算结果
     */
    void bottom_up_merge(
        std::vector<std::vector<std::uint32_t>>& level_results
    );
};

/* ================= 性能分析与自调优 ================= */

/**
 * @brief 自适应性能调优器
 */
class AdaptivePerformanceTuner {
private:
    struct PerformanceMetrics {
        double computation_time;
        double communication_time;
        double memory_bandwidth;
        double cache_miss_rate;
    };
    
    std::vector<PerformanceMetrics> history_;
    
public:
    /**
     * @brief 收集性能指标
     */
    void collect_metrics(const std::chrono::high_resolution_clock::time_point& start,
                        const std::chrono::high_resolution_clock::time_point& end);
    
    /**
     * @brief 基于历史性能数据自动调优
     */
    void auto_tune_parameters();
    
    /**
     * @brief 推荐最优配置
     */
    struct OptimalConfig {
        size_t block_size;
        int num_threads;
        bool use_prefetch;
        bool use_async_comm;
    };
    
    OptimalConfig recommend_config(size_t problem_size) const;
};

} // namespace ntt_advanced

#endif // NTT_ADVANCED_OPTIMIZATIONS_H 