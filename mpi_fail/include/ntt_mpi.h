/**
 * @file ntt_mpi.h
 * @brief NTT数论变换的MPI并行实现头文件
 * @details 提供基于MPI的分布式NTT算法，支持多种优化策略
 */

#ifndef NTT_MPI_H
#define NTT_MPI_H

#include <mpi.h>
#include <vector>
#include <cstdint>

namespace ntt_mpi {

/**
 * @struct MPIContext
 * @brief MPI上下文信息
 */
struct MPIContext {
    int rank;              // 当前进程编号
    int size;              // 总进程数
    MPI_Comm comm;         // 通信器
};

/**
 * @struct NTTConfig
 * @brief NTT配置参数
 */
struct NTTConfig {
    int n;                 // 多项式长度
    uint32_t p;           // 模数
    int g;                 // 原根
    int strategy;          // 并行策略 (0: 行划分, 1: 列划分, 2: 二维划分)
    int radix;             // 基数 (2,4,8)
    bool useBarrett;       // 是否启用Barrett规约
    
    // 新增的真正并行NTT所需的成员
    std::vector<uint32_t> w;   // 正向单位根表 (ω^0 … ω^{n/2-1})
    std::vector<uint32_t> iw;  // 逆向单位根表
    uint32_t inv_n;           // n的逆元 (mod p)
    
    // Barrett快速模运算方法
    uint32_t mul_mod(uint32_t a, uint32_t b) const {
        return ((uint64_t)a * b) % p;
    }
    
    uint32_t add_mod(uint32_t a, uint32_t b) const {
        uint64_t sum = (uint64_t)a + b;
        return sum >= p ? sum - p : sum;
    }
    
    uint32_t sub_mod(uint32_t a, uint32_t b) const {
        return a >= b ? a - b : a + p - b;
    }
    
    // 初始化单位根表
    void initRootTable(int length);
};

/**
 * @brief 初始化MPI环境
 * @param argc 命令行参数个数
 * @param argv 命令行参数数组
 * @return MPIContext MPI上下文
 */
MPIContext initializeMPI(int* argc, char*** argv);

/**
 * @brief 清理MPI环境
 */
void finalizeMPI();

/**
 * @brief 快速幂运算
 * @param base 底数
 * @param exp 指数
 * @param mod 模数
 * @return 计算结果
 */
int64_t quickPow(int64_t base, int64_t exp, int64_t mod);

/**
 * @brief 计算位反转数组
 * @param rev 位反转数组
 * @param n 数组长度
 */
void computeBitReverse(std::vector<int>& rev, int n);

/**
 * @brief 串行NTT变换
 * @param a 输入数据
 * @param n 数据长度
 * @param inverse 是否为逆变换
 * @param p 模数
 * @param g 原根
 */
void serialNTT(std::vector<int>& a, int n, bool inverse, int p, int g);

/**
 * @brief MPI分布式NTT变换
 * @param data 输入数据
 * @param n 数据长度
 * @param inverse 是否为逆变换
 * @param config NTT配置
 * @param ctx MPI上下文
 */
void distributedNTT(std::vector<int>& data, int n, bool inverse, 
                   const NTTConfig& config, const MPIContext& ctx);

/**
 * @brief 一维行划分的NTT实现
 * @param data 输入数据
 * @param n 数据长度
 * @param inverse 是否为逆变换
 * @param config NTT配置
 * @param ctx MPI上下文
 */
void nttRowPartition(std::vector<int>& data, int n, bool inverse,
                    const NTTConfig& config, const MPIContext& ctx);

/**
 * @brief 一维行划分的真正并行NTT实现 (uint32_t版本)
 * @param global 全局输入数据
 * @param n 数据长度
 * @param inverse 是否为逆变换
 * @param cfg NTT配置
 * @param ctx MPI上下文
 */
void nttRowPartition(std::vector<uint32_t>& global, int n, bool inverse,
                    const NTTConfig& cfg, const MPIContext& ctx);

/**
 * @brief 一维列划分的NTT实现
 * @param data 输入数据
 * @param n 数据长度
 * @param inverse 是否为逆变换
 * @param config NTT配置
 * @param ctx MPI上下文
 */
void nttColumnPartition(std::vector<int>& data, int n, bool inverse,
                       const NTTConfig& config, const MPIContext& ctx);

/**
 * @brief 一维列划分的真正并行NTT实现 (uint32_t版本)
 * @param global 全局输入数据
 * @param n 数据长度
 * @param inverse 是否为逆变换
 * @param cfg NTT配置
 * @param ctx MPI上下文
 */
void nttColumnPartition(std::vector<uint32_t>& global, int n, bool inverse,
                       const NTTConfig& cfg, const MPIContext& ctx);

/**
 * @brief 二维划分的NTT实现
 * @param data 输入数据
 * @param n 数据长度
 * @param inverse 是否为逆变换
 * @param config NTT配置
 * @param ctx MPI上下文
 */
void ntt2DPartition(std::vector<int>& data, int n, bool inverse,
                   const NTTConfig& config, const MPIContext& ctx);

/**
 * @brief 二维划分的真正并行NTT实现 (uint32_t版本)
 * @param global 全局输入数据
 * @param n 数据长度
 * @param inverse 是否为逆变换
 * @param cfg NTT配置
 * @param ctx MPI上下文
 */
void ntt2DPartition(std::vector<uint32_t>& global, int n, bool inverse,
                   const NTTConfig& cfg, const MPIContext& ctx);

/**
 * @brief MPI多项式乘法
 * @param a 多项式A
 * @param b 多项式B
 * @param result 结果多项式
 * @param n 多项式长度
 * @param config NTT配置
 * @param ctx MPI上下文
 */
void polynomialMultiply(const std::vector<int>& a, const std::vector<int>& b,
                       std::vector<int>& result, int n,
                       const NTTConfig& config, const MPIContext& ctx);

/**
 * @brief 数据读取函数
 * @param a 多项式A
 * @param b 多项式B
 * @param n 多项式长度
 * @param p 模数
 * @param input_id 输入文件ID
 */
void readData(std::vector<int>& a, std::vector<int>& b, int* n, int* p, int input_id);

/**
 * @brief 结果检查函数
 * @param result 计算结果
 * @param n 多项式长度
 * @param input_id 输入文件ID
 */
void checkResult(const std::vector<int>& result, int n, int input_id);

/**
 * @brief 结果写入函数
 * @param result 计算结果
 * @param n 多项式长度
 * @param input_id 输入文件ID
 */
void writeResult(const std::vector<int>& result, int n, int input_id);

/**
 * @brief 性能测试和分析
 * @param config NTT配置
 * @param ctx MPI上下文
 */
void performanceBenchmark(const NTTConfig& config, const MPIContext& ctx);

/**
 * @struct BarrettReducer
 * @brief Barrett快速模运算器
 */
struct BarrettReducer {
    uint64_t mod;
    uint64_t inv;
    BarrettReducer(uint64_t m);
    uint32_t reduce(uint64_t x) const;
    uint32_t mul(uint32_t a,uint32_t b) const;
};

/**
 * @brief Radix-4串行NTT (支持Barrett与SIMD)
 */
void serialNTTRadix4(std::vector<uint32_t>& a,bool inverse,const BarrettReducer& br);

/**
 * @brief Radix-8串行NTT (支持Barrett与SIMD)
 */
void serialNTTRadix8(std::vector<uint32_t>& a,bool inverse,const BarrettReducer& br);

/**
 * @brief CRT三模合并
 * @param r0 模p0的结果
 * @param r1 模p1的结果
 * @param r2 模p2的结果
 * @param p0 p0
 * @param p1 p1
 * @param p2 p2
 * @param out 合并后的64位结果
 */
void crtMergeThree(const std::vector<uint32_t>& r0,const std::vector<uint32_t>& r1,
                   const std::vector<uint32_t>& r2,uint32_t p0,uint32_t p1,uint32_t p2,
                   std::vector<uint64_t>& out);

#ifdef __ARM_NEON
/**
 * @brief SIMD优化的Radix-4 NTT
 */
void simdNTTRadix4(std::vector<uint32_t>& a,bool inverse,const BarrettReducer& br);
#endif

/**
 * @brief 高级MPI NTT: 分层并行 + Barrett + Radix优化
 */
void advancedMPINTT(std::vector<int>& data, int n, bool inverse,
                   const NTTConfig& config, const MPIContext& ctx);

/**
 * @brief 多模CRT并行多项式乘法 
 */
void multiModularMPIMultiply(const std::vector<int>& a, const std::vector<int>& b,
                            std::vector<uint64_t>& result, int n, const MPIContext& ctx);

/**
 * @brief 混合并行策略：数据+任务并行
 */
void hybridParallelNTT(std::vector<int>& data, int n, bool inverse,
                      const NTTConfig& config, const MPIContext& ctx);

} // namespace ntt_mpi

#endif // NTT_MPI_H 